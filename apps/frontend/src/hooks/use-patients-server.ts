"use client";

import { useState, useEffect, useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  bloodGroup?: string;
  maritalStatus?: string;
  occupation?: string;
  email?: string;
  phone: string;
  alternatePhone?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country: string;
  allergies?: string;
  chronicDiseases?: string;
  currentMedications?: string;
  familyMedicalHistory?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelation?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  insuranceExpiryDate?: string;
  registrationDate: string;
  status: string;
  primaryBranch?: {
    id: string;
    name: string;
  };
  user?: {
    id: string;
    name: string;
    email: string;
  };
  abhaProfile?: {
    id: string;
    abhaNumber: string;
    abhaAddress?: string;
    healthIdNumber?: string;
    abhaStatus?: string;
    kycVerified: boolean;
  };
  linkTokenRequests?: any[];
  abhaLinkTokens?: any[];
  createdAt: string;
  updatedAt: string;
}

interface Pagination {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface PatientsFilters {
  status?: string;
  search?: string;
  page?: number;
  limit?: number;
}

interface UsePatientsServerReturn {
  patients: Patient[];
  pagination: Pagination | null;
  isLoading: boolean;
  error: string | null;
  filters: PatientsFilters;
  setFilters: (newFilters: Partial<PatientsFilters>) => void;
  refetch: () => void;
  createPatient: (data: any) => Promise<Patient | null>;
  updatePatient: (id: string, data: any) => Promise<Patient | null>;
  deletePatient: (id: string, hardDelete?: boolean) => Promise<boolean>;
}

export function usePatientsServer(initialFilters: PatientsFilters = {}): UsePatientsServerReturn {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  
  const [patients, setPatients] = useState<Patient[]>([]);
  const [pagination, setPagination] = useState<Pagination | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFiltersState] = useState<PatientsFilters>({
    page: 1,
    limit: 50,
    ...initialFilters,
  });

  // Fetch patients function
  const fetchPatients = useCallback(async (currentFilters: PatientsFilters) => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();

      if (currentFilters.status && currentFilters.status !== "all") {
        params.append("filter_status", currentFilters.status);
      }

      if (currentFilters.search) {
        params.append("search", currentFilters.search);
      }

      if (currentFilters.page) {
        params.append("page", currentFilters.page.toString());
      }

      if (currentFilters.limit) {
        params.append("limit", currentFilters.limit.toString());
      }

      const response = await fetch(`/api/generic/patient?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch patients");
      }

      const data = await response.json();
      setPatients(data.data || []);
      setPagination(data.pagination || null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch patients";
      setError(errorMessage);
      console.error("Error fetching patients:", err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Set filters and trigger fetch
  const setFilters = useCallback((newFilters: Partial<PatientsFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    
    // Reset to page 1 when filters change (except when changing page)
    if (!newFilters.page && (newFilters.status !== undefined || newFilters.search !== undefined)) {
      updatedFilters.page = 1;
    }

    setFiltersState(updatedFilters);
    
    // Update URL
    startTransition(() => {
      const params = new URLSearchParams();
      
      Object.entries(updatedFilters).forEach(([key, value]) => {
        if (value && value !== "all") {
          params.set(key, value.toString());
        }
      });

      const newUrl = `/patients${params.toString() ? `?${params.toString()}` : ""}`;
      router.push(newUrl);
    });
  }, [filters, router]);

  // Refetch with current filters
  const refetch = useCallback(() => {
    fetchPatients(filters);
  }, [fetchPatients, filters]);

  // Create patient
  const createPatient = useCallback(async (data: any): Promise<Patient | null> => {
    try {
      const response = await fetch("/api/generic/patient", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create patient");
      }

      const result = await response.json();
      const newPatient = result.data;

      // Add to current patients list
      setPatients(prev => [newPatient, ...prev]);
      
      toast.success("Patient created successfully");
      return newPatient;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to create patient";
      toast.error(errorMessage);
      console.error("Error creating patient:", err);
      return null;
    }
  }, []);

  // Update patient
  const updatePatient = useCallback(async (id: string, data: any): Promise<Patient | null> => {
    try {
      const response = await fetch(`/api/generic/patient/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update patient");
      }

      const result = await response.json();
      const updatedPatient = result.data;

      // Update in current patients list
      setPatients(prev => 
        prev.map(patient => 
          patient.id === id ? updatedPatient : patient
        )
      );
      
      toast.success("Patient updated successfully");
      return updatedPatient;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update patient";
      toast.error(errorMessage);
      console.error("Error updating patient:", err);
      return null;
    }
  }, []);

  // Delete patient
  const deletePatient = useCallback(async (id: string, hardDelete: boolean = false): Promise<boolean> => {
    try {
      const params = hardDelete ? "?soft=false" : "?soft=true";
      const response = await fetch(`/api/generic/patient/${id}${params}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete patient");
      }

      // Remove from current patients list or update status
      if (hardDelete) {
        setPatients(prev => prev.filter(patient => patient.id !== id));
      } else {
        setPatients(prev => 
          prev.map(patient => 
            patient.id === id ? { ...patient, status: "deleted" } : patient
          )
        );
      }
      
      toast.success(`Patient ${hardDelete ? "deleted" : "archived"} successfully`);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to delete patient";
      toast.error(errorMessage);
      console.error("Error deleting patient:", err);
      return false;
    }
  }, []);

  // Fetch patients when filters change
  useEffect(() => {
    fetchPatients(filters);
  }, [fetchPatients, filters]);

  return {
    patients,
    pagination,
    isLoading: isLoading || isPending,
    error,
    filters,
    setFilters,
    refetch,
    createPatient,
    updatePatient,
    deletePatient,
  };
}
