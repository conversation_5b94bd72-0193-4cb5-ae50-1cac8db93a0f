import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  LucideIcon,
  Search,
  Users,
  FileText,
  Calendar,
  Shield,
  FileCheck
} from "lucide-react";

interface EmptyStateProps {
  icon?: LucideIcon;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
    variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  };
  className?: string;
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  action,
  secondaryAction,
  className = "",
}: EmptyStateProps) {
  return (
    <Card className={`border-dashed ${className}`}>
      <CardContent className="flex flex-col items-center justify-center py-12 px-6 text-center">
        {Icon && (
          <div className="mb-4 p-3 rounded-full bg-muted">
            <Icon className="h-8 w-8 text-muted-foreground" />
          </div>
        )}
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-sm text-muted-foreground mb-6 max-w-md">{description}</p>
        {(action || secondaryAction) && (
          <div className="flex flex-col sm:flex-row gap-3">
            {action && (
              <Button
                onClick={action.onClick}
                variant={action.variant || "default"}
              >
                {action.label}
              </Button>
            )}
            {secondaryAction && (
              <Button
                onClick={secondaryAction.onClick}
                variant={secondaryAction.variant || "outline"}
              >
                {secondaryAction.label}
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Specialized empty state components for common scenarios
interface PatientsEmptyStateProps {
  searchQuery?: string;
  statusFilter?: string;
  onCreatePatient: () => void;
  onClearFilters?: () => void;
}

export function PatientsEmptyState({
  searchQuery,
  statusFilter,
  onCreatePatient,
  onClearFilters,
}: PatientsEmptyStateProps) {
  const isFiltered = searchQuery || (statusFilter && statusFilter !== "active");
  
  if (isFiltered) {
    return (
      <EmptyState
        icon={Search}
        title="No patients found"
        description={`No patients match your current search${searchQuery ? ` for "${searchQuery}"` : ""} and filter criteria. Try adjusting your search terms or filters.`}
        action={{
          label: "Clear Filters",
          onClick: onClearFilters || (() => {}),
          variant: "outline",
        }}
        secondaryAction={{
          label: "Add New Patient",
          onClick: onCreatePatient,
        }}
      />
    );
  }

  return (
    <EmptyState
      icon={Users}
      title="No patients yet"
      description="Get started by adding your first patient to the system. You can register new patients or import existing patient data."
      action={{
        label: "Add First Patient",
        onClick: onCreatePatient,
      }}
    />
  );
}

interface ConsultationsEmptyStateProps {
  patientName?: string;
  onCreateConsultation: () => void;
}

export function ConsultationsEmptyState({
  patientName,
  onCreateConsultation,
}: ConsultationsEmptyStateProps) {
  return (
    <EmptyState
      icon={FileText}
      title="No consultations yet"
      description={`${patientName ? `${patientName} hasn't` : "This patient hasn't"} had any consultations yet. Start the first consultation to begin tracking their medical history.`}
      action={{
        label: "Start Consultation",
        onClick: onCreateConsultation,
      }}
    />
  );
}

interface AppointmentsEmptyStateProps {
  patientName?: string;
  onCreateAppointment: () => void;
}

export function AppointmentsEmptyState({
  patientName,
  onCreateAppointment,
}: AppointmentsEmptyStateProps) {
  return (
    <EmptyState
      icon={Calendar}
      title="No appointments scheduled"
      description={`${patientName ? `${patientName} has` : "This patient has"} no upcoming appointments. Schedule a new appointment to continue their care.`}
      action={{
        label: "Schedule Appointment",
        onClick: onCreateAppointment,
      }}
    />
  );
}

interface DocumentsEmptyStateProps {
  patientName?: string;
  onUploadDocument: () => void;
}

export function DocumentsEmptyState({
  patientName,
  onUploadDocument,
}: DocumentsEmptyStateProps) {
  return (
    <EmptyState
      icon={FileText}
      title="No documents uploaded"
      description={`${patientName ? `${patientName} has` : "This patient has"} no documents uploaded yet. Upload medical records, reports, or other important documents.`}
      action={{
        label: "Upload Document",
        onClick: onUploadDocument,
      }}
    />
  );
}

interface ConsentEmptyStateProps {
  patientName?: string;
  hasAbhaProfile?: boolean;
  onRequestConsent: () => void;
  onSetupAbha?: () => void;
}

export function ConsentEmptyState({
  patientName,
  hasAbhaProfile,
  onRequestConsent,
  onSetupAbha,
}: ConsentEmptyStateProps) {
  if (!hasAbhaProfile) {
    return (
      <EmptyState
        icon={Shield}
        title="ABHA Profile Required"
        description={`${patientName ? `${patientName} needs` : "This patient needs"} an ABHA profile to manage consents. Set up their ABHA profile first to enable consent management.`}
        action={{
          label: "Setup ABHA Profile",
          onClick: onSetupAbha || (() => {}),
        }}
      />
    );
  }

  return (
    <EmptyState
      icon={FileCheck}
      title="No consents yet"
      description={`${patientName ? `${patientName} has` : "This patient has"} no ABHA consents yet. Request consent to access their health records from other healthcare providers.`}
      action={{
        label: "Request Consent",
        onClick: onRequestConsent,
      }}
    />
  );
}
