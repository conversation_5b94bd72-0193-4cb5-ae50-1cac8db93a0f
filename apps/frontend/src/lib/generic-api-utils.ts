import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/session";
import { cookies } from "next/headers";


// DOCTYPE to Prisma model mapping
export const DOCTYPE_MODEL_MAP: Record<string, string> = {
  // Core entities
  patient: "patient",
  doctor: "doctor",
  consultation: "consultation",
  appointment: "appointment",
  organization: "organization",
  branch: "branch",
  department: "department",
  user: "user",
  
  // Medical records
  vitals: "vitals",
  prescription: "prescription",
  clinicalNote: "clinicalNote",
  diagnosticReport: "diagnosticReport",
  immunization: "immunization",
  procedure: "procedure",
  allergyIntolerance: "allergyIntolerance",
  
  // Administrative
  invoice: "invoice",
  labTestRequest: "labTestRequest",
  queueStatus: "queueStatus",
  careType: "careType",
  
  // ABDM/Health records
  abhaProfile: "abhaProfile",
  consent: "consent",
  careContext: "careContext",
  documentReference: "documentReference",
  fhirBundle: "fhirBundle",
  fhirResource: "fhirResource",
  
  // Staff and scheduling
  staff: "staff",
  doctorSchedule: "doctorSchedule",
  scheduleSlot: "scheduleSlot",
  scheduleOverride: "scheduleOverride",
};

// Fields that require organization-based filtering
export const ORGANIZATION_FILTERED_MODELS = [
  "patient", "doctor", "consultation", "appointment", "branch", "department",
  "vitals", "prescription", "clinicalNote", "diagnosticReport", "immunization",
  "procedure", "allergyIntolerance", "invoice", "labTestRequest", "queueStatus",
  "careType", "abhaProfile", "consent", "careContext", "documentReference",
  "fhirBundle", "fhirResource", "staff", "doctorSchedule"
];

// Models that have soft delete (status field)
export const SOFT_DELETE_MODELS = [
  "patient", "doctor", "organization", "branch", "department", "user",
  "staff", "doctorSchedule", "careType"
];

// Authentication and authorization utilities
export async function validateAuth(): Promise<{
  user: any;
  organizationId: string;
  error?: NextResponse;
}> {
  const user = await getCurrentUser();
  if (!user) {
    return {
      user: null,
      organizationId: "",
      error: NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    };
  }

  const userInfoCookie = cookies().get("user-info")?.value;
  let organizationId = "";

  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(userInfoCookie);
      organizationId = userInfo.organizationId || "";
    } catch (error) {
      console.error("Error parsing user info:", error);
    }
  }

  if (!organizationId) {
    return {
      user,
      organizationId: "",
      error: NextResponse.json(
        { error: "No organization found" },
        { status: 404 }
      )
    };
  }

  return { user, organizationId };
}

// Validate DOCTYPE
export function validateDocType(doctype: string): {
  isValid: boolean;
  modelName?: string;
  error?: NextResponse;
} {
  const modelName = DOCTYPE_MODEL_MAP[doctype.toLowerCase()];
  
  if (!modelName) {
    return {
      isValid: false,
      error: NextResponse.json(
        { 
          error: "Invalid DOCTYPE",
          supportedTypes: Object.keys(DOCTYPE_MODEL_MAP)
        },
        { status: 400 }
      )
    };
  }

  return { isValid: true, modelName };
}

// Build where clause with organization filtering
export function buildWhereClause(
  modelName: string,
  organizationId: string,
  additionalFilters: any = {}
): any {
  let where = { ...additionalFilters };

  // Add organization filter if model supports it
  if (ORGANIZATION_FILTERED_MODELS.includes(modelName)) {
    where.organizationId = organizationId;
  }

  // Filter out soft-deleted records by default (only if no status filter is explicitly provided)
  if (SOFT_DELETE_MODELS.includes(modelName) && !where.status) {
    where.status = { not: "deleted" };
  }

  return where;
}

// Get Prisma model delegate
export function getModelDelegate(modelName: string): any {
  // @ts-ignore - Dynamic model access
  return db[modelName];
}

// Standard error responses
export const ErrorResponses = {
  unauthorized: () => NextResponse.json({ error: "Unauthorized" }, { status: 401 }),
  forbidden: () => NextResponse.json({ error: "Forbidden" }, { status: 403 }),
  notFound: (resource = "Resource") => NextResponse.json(
    { error: `${resource} not found` },
    { status: 404 }
  ),
  badRequest: (message = "Bad request") => NextResponse.json(
    { error: message },
    { status: 400 }
  ),
  conflict: (message = "Resource already exists") => NextResponse.json(
    { error: message },
    { status: 409 }
  ),
  serverError: (message = "Internal server error") => NextResponse.json(
    { error: message },
    { status: 500 }
  ),
  validationError: (errors: any) => NextResponse.json(
    { error: "Validation failed", details: errors },
    { status: 422 }
  )
};

// Pagination utilities
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export function parsePaginationParams(searchParams: URLSearchParams): PaginationParams {
  const page = parseInt(searchParams.get("page") || "1");
  const limit = Math.min(parseInt(searchParams.get("limit") || "50"), 100); // Max 100 records
  const offset = (page - 1) * limit;

  return { page, limit, offset };
}

// Search utilities
export function parseSearchParams(searchParams: URLSearchParams): {
  search?: string;
  filters: Record<string, any>;
  sort: Record<string, "asc" | "desc">;
} {
  const search = searchParams.get("search") || undefined;
  const filters: Record<string, any> = {};
  const sort: Record<string, "asc" | "desc"> = {};

  // Parse filters (filter_fieldName=value)
  for (const [key, value] of searchParams.entries()) {
    if (key.startsWith("filter_")) {
      const fieldName = key.replace("filter_", "");
      filters[fieldName] = value;
    }
  }

  // Parse sorting (sort_fieldName=asc|desc)
  for (const [key, value] of searchParams.entries()) {
    if (key.startsWith("sort_")) {
      const fieldName = key.replace("sort_", "");
      if (value === "asc" || value === "desc") {
        sort[fieldName] = value;
      }
    }
  }

  // Default sort by createdAt desc if no sort specified
  if (Object.keys(sort).length === 0) {
    sort.createdAt = "desc";
  }

  return { search, filters, sort };
}

// Model-specific include configurations for better data fetching
export const MODEL_INCLUDES: Record<string, any> = {
  patient: {
    primaryBranch: {
      select: { id: true, name: true }
    },
    user: {
      select: { id: true, name: true, email: true }
    },
    abhaProfile: true,
    organization: {
      select: { id: true, name: true }
    }
  },
  doctor: {
    user: {
      select: { id: true, name: true, email: true }
    },
    department: {
      select: { id: true, name: true }
    },
    organization: {
      select: { id: true, name: true }
    },
    branches: {
      include: { branch: { select: { id: true, name: true } } }
    }
  },
  consultation: {
    patient: {
      select: { id: true, firstName: true, lastName: true, dateOfBirth: true, gender: true }
    },
    doctor: {
      select: {
        id: true,
        user: { select: { id: true, name: true } },
        specialization: true
      }
    },
    branch: {
      select: { id: true, name: true }
    },
    organization: {
      select: { id: true, name: true }
    }
  },
  appointment: {
    patient: {
      select: { id: true, firstName: true, lastName: true }
    },
    doctor: {
      select: {
        id: true,
        user: { select: { id: true, name: true } }
      }
    },
    branch: {
      select: { id: true, name: true }
    }
  },
  vitals: {
    patient: {
      select: { id: true, firstName: true, lastName: true }
    },
    doctor: {
      select: {
        id: true,
        user: { select: { id: true, name: true } }
      }
    },
    consultation: {
      select: { id: true, consultationDate: true }
    }
  },
  prescription: {
    patient: {
      select: { id: true, firstName: true, lastName: true }
    },
    doctor: {
      select: {
        id: true,
        user: { select: { id: true, name: true } }
      }
    },
    consultation: {
      select: { id: true, consultationDate: true }
    },
    items: true
  }
};

// Get appropriate include for a model
export function getModelInclude(modelName: string): any {
  return MODEL_INCLUDES[modelName] || {};
}

// Validation utilities
export function validateRequiredFields(data: any, requiredFields: string[]): {
  isValid: boolean;
  missingFields?: string[];
} {
  const missingFields = requiredFields.filter(field => !data[field]);

  if (missingFields.length > 0) {
    return { isValid: false, missingFields };
  }

  return { isValid: true };
}

// Model-specific required fields for creation
export const MODEL_REQUIRED_FIELDS: Record<string, string[]> = {
  patient: ["firstName", "lastName", "dateOfBirth", "gender", "phone"],
  doctor: ["userId", "departmentId", "organizationId"],
  consultation: ["patientId", "doctorId", "branchId", "organizationId"],
  appointment: ["patientId", "doctorId", "branchId", "appointmentDate", "startTime", "endTime"],
  vitals: ["consultationId", "patientId", "doctorId", "organizationId"],
  prescription: ["consultationId", "patientId", "doctorId", "organizationId"],
  clinicalNote: ["consultationId", "patientId", "doctorId", "organizationId"],
  diagnosticReport: ["consultationId", "patientId", "doctorId", "organizationId", "reportType", "code", "codeDisplay"],
  immunization: ["patientId", "doctorId", "organizationId", "vaccineCode", "status"],
  procedure: ["patientId", "doctorId", "organizationId", "code", "status"],
  allergyIntolerance: ["patientId", "doctorId", "organizationId", "code", "clinicalStatus"],
  invoice: ["patientId", "organizationId", "totalAmount", "status"],
  labTestRequest: ["consultationId", "patientId", "doctorId", "organizationId", "branchId", "testType", "testName"],
  careType: ["name", "price", "organizationId", "departmentId"],
  staff: ["organizationId", "role", "email", "name"],
  branch: ["name", "organizationId"],
  department: ["name", "organizationId", "departmentCode"]
};

// Get required fields for a model
export function getRequiredFields(modelName: string): string[] {
  return MODEL_REQUIRED_FIELDS[modelName] || [];
}

// Audit logging utility
export async function logApiActivity(
  action: string,
  modelName: string,
  recordId: string,
  userId: string,
  organizationId: string,
  details?: any
) {
  try {
    // In a real implementation, you would log to an audit table
    console.log("API Activity:", {
      timestamp: new Date().toISOString(),
      action,
      modelName,
      recordId,
      userId,
      organizationId,
      details
    });

    // TODO: Implement actual audit logging to database
    // await db.auditLog.create({
    //   data: {
    //     action,
    //     modelName,
    //     recordId,
    //     userId,
    //     organizationId,
    //     details: details ? JSON.stringify(details) : null,
    //     timestamp: new Date()
    //   }
    // });
  } catch (error) {
    console.error("Failed to log API activity:", error);
  }
}
