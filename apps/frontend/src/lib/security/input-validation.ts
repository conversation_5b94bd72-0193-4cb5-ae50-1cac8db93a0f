/**
 * Input Validation Middleware for API Routes
 * 
 * Provides comprehensive input validation and sanitization
 * to prevent SQL injection and other security vulnerabilities.
 */

import { NextRequest, NextResponse } from "next/server";
import { InputValidator, SecurityErrorHandler } from "./sql-injection-prevention";

/**
 * Validation schemas for common input types
 */
export const ValidationSchemas = {
  uuid: (value: string) => InputValidator.isValidUUID(value),
  email: (value: string) => InputValidator.isValidEmail(value),
  phone: (value: string) => InputValidator.isValidPhone(value),
  safeString: (value: string) => InputValidator.validateNoSQLInjection(value),
  positiveInteger: (value: string) => {
    const num = parseInt(value, 10);
    return !isNaN(num) && num > 0;
  },
  nonNegativeInteger: (value: string) => {
    const num = parseInt(value, 10);
    return !isNaN(num) && num >= 0;
  },
  alphanumeric: (value: string) => /^[a-zA-Z0-9]+$/.test(value),
  slug: (value: string) => /^[a-zA-Z0-9-_]+$/.test(value),
  dateString: (value: string) => !isNaN(Date.parse(value)),
  sortDirection: (value: string) => ['asc', 'desc', 'ASC', 'DESC'].includes(value)
};

/**
 * Parameter validation configuration
 */
interface ValidationRule {
  required?: boolean;
  type: keyof typeof ValidationSchemas;
  maxLength?: number;
  minLength?: number;
  customValidator?: (value: string) => boolean;
}

interface ValidationConfig {
  params?: Record<string, ValidationRule>;
  query?: Record<string, ValidationRule>;
  body?: Record<string, ValidationRule>;
}

/**
 * Input validation middleware
 */
export function validateInput(config: ValidationConfig) {
  return async (req: NextRequest, context?: any) => {
    try {
      const errors: string[] = [];

      // Validate URL parameters
      if (config.params && context?.params) {
        for (const [key, rule] of Object.entries(config.params)) {
          const value = context.params[key];
          const error = validateField(key, value, rule, 'parameter');
          if (error) errors.push(error);
        }
      }

      // Validate query parameters
      if (config.query) {
        const searchParams = new URL(req.url).searchParams;
        for (const [key, rule] of Object.entries(config.query)) {
          const value = searchParams.get(key);
          const error = validateField(key, value, rule, 'query parameter');
          if (error) errors.push(error);
        }
      }

      // Validate request body
      if (config.body && (req.method === 'POST' || req.method === 'PATCH' || req.method === 'PUT')) {
        try {
          const body = await req.json();
          for (const [key, rule] of Object.entries(config.body)) {
            const value = body[key];
            const error = validateField(key, value, rule, 'body field');
            if (error) errors.push(error);
          }
        } catch (e) {
          errors.push('Invalid JSON in request body');
        }
      }

      // Return validation errors if any
      if (errors.length > 0) {
        return NextResponse.json(
          {
            error: 'Validation failed',
            details: errors,
            timestamp: new Date().toISOString()
          },
          { status: 400 }
        );
      }

      return null; // No validation errors
    } catch (error) {
      console.error('Input validation error:', error);
      return NextResponse.json(
        SecurityErrorHandler.createSafeErrorResponse(error),
        { status: 500 }
      );
    }
  };
}

/**
 * Validates a single field against its rule
 */
function validateField(
  fieldName: string,
  value: any,
  rule: ValidationRule,
  fieldType: string
): string | null {
  // Check if required field is missing
  if (rule.required && (value === null || value === undefined || value === '')) {
    return `${fieldType} '${fieldName}' is required`;
  }

  // Skip validation if field is optional and not provided
  if (!rule.required && (value === null || value === undefined || value === '')) {
    return null;
  }

  // Convert to string for validation
  const stringValue = String(value);

  // Check length constraints
  if (rule.minLength && stringValue.length < rule.minLength) {
    return `${fieldType} '${fieldName}' must be at least ${rule.minLength} characters`;
  }

  if (rule.maxLength && stringValue.length > rule.maxLength) {
    return `${fieldType} '${fieldName}' must be at most ${rule.maxLength} characters`;
  }

  // Validate type
  const validator = ValidationSchemas[rule.type];
  if (!validator(stringValue)) {
    return `${fieldType} '${fieldName}' has invalid format for type '${rule.type}'`;
  }

  // Custom validation
  if (rule.customValidator && !rule.customValidator(stringValue)) {
    return `${fieldType} '${fieldName}' failed custom validation`;
  }

  return null;
}

/**
 * Common validation configurations for API routes
 */
export const CommonValidations = {
  // Patient ID validation
  patientId: {
    params: {
      id: { required: true, type: 'uuid' as const }
    }
  },

  // Doctor ID validation
  doctorId: {
    params: {
      id: { required: true, type: 'uuid' as const }
    }
  },

  // Pagination validation
  pagination: {
    query: {
      page: { required: false, type: 'positiveInteger' as const },
      limit: { required: false, type: 'positiveInteger' as const, customValidator: (v) => parseInt(v) <= 100 },
      offset: { required: false, type: 'nonNegativeInteger' as const }
    }
  },

  // Search validation
  search: {
    query: {
      search: { required: false, type: 'safeString' as const, maxLength: 100 },
      sort: { required: false, type: 'alphanumeric' as const },
      order: { required: false, type: 'sortDirection' as const }
    }
  },

  // Date range validation
  dateRange: {
    query: {
      fromDate: { required: false, type: 'dateString' as const },
      toDate: { required: false, type: 'dateString' as const }
    }
  },

  // ABHA validation
  abhaData: {
    body: {
      abhaNumber: { required: false, type: 'alphanumeric' as const, minLength: 14, maxLength: 14 },
      abhaAddress: { required: false, type: 'safeString' as const, maxLength: 100 }
    }
  }
};

/**
 * Sanitizes request data to prevent injection attacks
 */
export function sanitizeRequestData(data: any): any {
  if (typeof data === 'string') {
    return InputValidator.sanitizeString(data);
  }

  if (Array.isArray(data)) {
    return data.map(item => sanitizeRequestData(item));
  }

  if (data && typeof data === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeRequestData(value);
    }
    return sanitized;
  }

  return data;
}

/**
 * Rate limiting configuration for different endpoint types
 */
export const RateLimitConfig = {
  // Standard API endpoints
  standard: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
  },

  // Authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5 // limit each IP to 5 requests per windowMs
  },

  // Search endpoints
  search: {
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 30 // limit each IP to 30 requests per windowMs
  },

  // Webhook endpoints
  webhook: {
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 60 // limit each IP to 60 requests per windowMs
  }
};

/**
 * Security headers configuration
 */
export const SecurityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
};

/**
 * Applies security headers to response
 */
export function applySecurityHeaders(response: NextResponse): NextResponse {
  Object.entries(SecurityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  return response;
}
