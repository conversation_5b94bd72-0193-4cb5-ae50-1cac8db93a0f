/**
 * SQL Injection Prevention Utilities
 * 
 * This module provides utilities to prevent SQL injection attacks
 * and validate database queries for security compliance.
 */

import { Prisma } from "@prisma/client";

/**
 * Input validation and sanitization utilities
 */
export class InputValidator {
  /**
   * Validates and sanitizes string input to prevent SQL injection
   */
  static sanitizeString(input: string): string {
    if (typeof input !== 'string') {
      throw new Error('Input must be a string');
    }
    
    // Remove or escape potentially dangerous characters
    return input
      .replace(/'/g, "''")  // Escape single quotes
      .replace(/;/g, '')    // Remove semicolons
      .replace(/--/g, '')   // Remove SQL comments
      .replace(/\/\*/g, '') // Remove block comment start
      .replace(/\*\//g, '') // Remove block comment end
      .trim();
  }

  /**
   * Validates that a string doesn't contain SQL injection patterns
   */
  static validateNoSQLInjection(input: string): boolean {
    const sqlInjectionPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b)/i,
      /(\b(UNION|OR|AND)\s+\d+\s*=\s*\d+)/i,
      /(--|\/\*|\*\/)/,
      /(\b(SLEEP|WAITFOR|DELAY)\b)/i,
      /(\b(INFORMATION_SCHEMA|SYS\.)/i,
      /(\b(XP_|SP_)\w+)/i,
      /(;\s*(DROP|DELETE|INSERT|UPDATE))/i
    ];

    return !sqlInjectionPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Validates UUID format
   */
  static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Validates email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validates phone number format
   */
  static isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,15}$/;
    return phoneRegex.test(phone);
  }
}

/**
 * Secure query builder utilities
 */
export class SecureQueryBuilder {
  /**
   * Builds a secure LIKE query with parameterized values
   */
  static buildLikeQuery(field: string, value: string): Prisma.Sql {
    const sanitizedValue = InputValidator.sanitizeString(value);
    return Prisma.sql`${Prisma.raw(field)} LIKE ${`%${sanitizedValue}%`}`;
  }

  /**
   * Builds a secure WHERE IN query with parameterized values
   */
  static buildInQuery(field: string, values: string[]): Prisma.Sql {
    const sanitizedValues = values.map(v => InputValidator.sanitizeString(v));
    return Prisma.sql`${Prisma.raw(field)} IN (${Prisma.join(sanitizedValues)})`;
  }

  /**
   * Builds a secure ORDER BY clause
   */
  static buildOrderBy(field: string, direction: 'ASC' | 'DESC' = 'ASC'): Prisma.Sql {
    // Validate field name to prevent injection
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(field)) {
      throw new Error('Invalid field name for ORDER BY');
    }
    
    if (!['ASC', 'DESC'].includes(direction)) {
      throw new Error('Invalid sort direction');
    }
    
    return Prisma.sql`ORDER BY ${Prisma.raw(`"${field}"`)} ${Prisma.raw(direction)}`;
  }

  /**
   * Builds a secure LIMIT clause
   */
  static buildLimit(limit: number, offset: number = 0): Prisma.Sql {
    if (!Number.isInteger(limit) || limit < 0 || limit > 1000) {
      throw new Error('Invalid limit value');
    }
    
    if (!Number.isInteger(offset) || offset < 0) {
      throw new Error('Invalid offset value');
    }
    
    return Prisma.sql`LIMIT ${limit} OFFSET ${offset}`;
  }
}

/**
 * Query audit and logging utilities
 */
export class QueryAuditor {
  /**
   * Logs database queries for security audit
   */
  static async logQuery(
    operation: string,
    model: string,
    userId?: string,
    organizationId?: string,
    metadata?: any
  ): Promise<void> {
    const logEntry = {
      timestamp: new Date().toISOString(),
      operation,
      model,
      userId,
      organizationId,
      metadata,
      source: 'database-query'
    };

    // In production, this should write to a secure audit log
    console.log('DB_AUDIT:', JSON.stringify(logEntry));
  }

  /**
   * Validates that a query is using parameterized statements
   */
  static validateParameterizedQuery(query: string): boolean {
    // Check for string interpolation patterns that indicate potential SQL injection
    const dangerousPatterns = [
      /\$\{[^}]+\}/,  // Template literal interpolation
      /'\s*\+\s*\w+/,  // String concatenation
      /"\s*\+\s*\w+/,  // String concatenation
    ];

    return !dangerousPatterns.some(pattern => pattern.test(query));
  }
}

/**
 * Middleware for automatic SQL injection prevention
 */
export function createSecurityMiddleware() {
  return async (params: any, next: any) => {
    const start = Date.now();
    
    try {
      // Log the query for audit
      await QueryAuditor.logQuery(
        params.action,
        params.model || 'unknown',
        undefined, // userId would come from context
        undefined, // organizationId would come from context
        { args: params.args }
      );

      const result = await next(params);
      
      const duration = Date.now() - start;
      
      // Log successful query completion
      console.log(`DB_QUERY_SUCCESS: ${params.model}.${params.action} completed in ${duration}ms`);
      
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      
      // Log query errors for security monitoring
      console.error(`DB_QUERY_ERROR: ${params.model}.${params.action} failed after ${duration}ms:`, error);
      
      throw error;
    }
  };
}

/**
 * Error handling utilities
 */
export class SecurityErrorHandler {
  /**
   * Sanitizes error messages to prevent information disclosure
   */
  static sanitizeErrorMessage(error: any): string {
    if (error instanceof Error) {
      // Remove sensitive information from error messages
      const sanitized = error.message
        .replace(/password/gi, '[REDACTED]')
        .replace(/token/gi, '[REDACTED]')
        .replace(/key/gi, '[REDACTED]')
        .replace(/secret/gi, '[REDACTED]');
      
      return sanitized;
    }
    
    return 'An error occurred';
  }

  /**
   * Creates a safe error response for API endpoints
   */
  static createSafeErrorResponse(error: any, includeDetails: boolean = false) {
    const baseResponse = {
      error: 'An error occurred',
      timestamp: new Date().toISOString()
    };

    if (includeDetails && process.env.NODE_ENV === 'development') {
      return {
        ...baseResponse,
        details: this.sanitizeErrorMessage(error)
      };
    }

    return baseResponse;
  }
}

/**
 * Configuration for security settings
 */
export const SECURITY_CONFIG = {
  MAX_QUERY_LIMIT: 1000,
  MAX_STRING_LENGTH: 10000,
  ALLOWED_SORT_FIELDS: [
    'id', 'createdAt', 'updatedAt', 'name', 'email', 'status'
  ],
  QUERY_TIMEOUT_MS: 30000,
  ENABLE_QUERY_LOGGING: process.env.NODE_ENV !== 'test'
};
