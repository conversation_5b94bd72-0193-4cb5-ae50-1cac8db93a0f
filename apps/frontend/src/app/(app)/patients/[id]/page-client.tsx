"use client";

import React, { useState, useEffect } from "react";
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  User,
  Phone,
  Heart,
  AlertCircle,
  ShieldCheck,
  <PERSON><PERSON><PERSON>,
  Id<PERSON>ard,
  <PERSON>,
  Link as LinkIcon,
  Trash2,
  Calendar,
  MapPin,
  Mail,
  Activity,
  Archive,
  MoreVertical,
  FileText,
  Eye,
  Plus,
  CheckCircle,
} from "lucide-react";
import { formatDate } from "@/lib/utils";
import { formatGenderDisplay } from "@/lib/gender-utils";
import { toast } from "sonner";
import { ConsultationTimeline } from "@/components/consultations/consultation-timeline";
import { deletePatient, updatePatient } from "../actions";


interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  bloodGroup?: string;
  maritalStatus?: string;
  occupation?: string;
  email?: string;
  phone: string;
  alternatePhone?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country: string;
  allergies?: string;
  chronicDiseases?: string;
  currentMedications?: string;
  familyMedicalHistory?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelation?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  insuranceExpiryDate?: string;
  registrationDate: string;
  status: string;
  primaryBranch?: {
    id: string;
    name: string;
  };
  user?: {
    id: string;
    name: string;
    email: string;
  };
  abhaProfile?: {
    id: string;
    abhaNumber: string;
    abhaAddress?: string;
    healthIdNumber?: string;
    abhaStatus?: string;
    kycVerified: boolean;
  };
  documents?: any[];
  createdAt: string;
  updatedAt: string;
}

interface PatientDetailPageClientProps {
  patient: Patient;
  appointments: any[];
  consultations: any[];
  initialTab?: string;
  showSuccessMessage?: boolean;
}

export function PatientDetailPageClient({
  patient,
  appointments: _appointments,
  consultations: _consultations,
  initialTab = "personal",
  showSuccessMessage = false,
}: PatientDetailPageClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams?.get('tab') || initialTab);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteType, setDeleteType] = useState<'soft' | 'hard'>('soft');
  const [isDeleting, setIsDeleting] = useState(false);

  // Show success message if redirected after creation/update
  useEffect(() => {
    if (showSuccessMessage) {
      toast.success("Patient information updated successfully!");
    }
  }, [showSuccessMessage]);

  // Sync tab state with URL changes
  useEffect(() => {
    const tab = searchParams?.get('tab');
    if (tab && tab !== activeTab) {
      setActiveTab(tab);
    }
  }, [searchParams, activeTab]);

  // Calculate age
  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  // Handle patient deletion
  const handleDeletePatient = async () => {
    setIsDeleting(true);
    try {
      const isHardDelete = deleteType === 'hard';
      await deletePatient(patient.id, !isHardDelete);

      const successMessage = isHardDelete
        ? "Patient permanently deleted"
        : "Patient archived successfully";

      toast.success(successMessage);
      router.push("/patients");
    } catch (error) {
      console.error("Error deleting patient:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete patient");
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  // Handle delete button clicks
  const handleSoftDelete = () => {
    setDeleteType('soft');
    setShowDeleteDialog(true);
  };

  const handleHardDelete = () => {
    setDeleteType('hard');
    setShowDeleteDialog(true);
  };

  // Handle unarchive
  const handleUnarchive = async () => {
    setIsDeleting(true);
    try {
      await updatePatient(patient.id, { status: 'active' });
      toast.success("Patient unarchived successfully");
      router.refresh();
    } catch (error) {
      console.error('Error unarchiving patient:', error);
      toast.error("Failed to unarchive patient");
    } finally {
      setIsDeleting(false);
    }
  };



  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-6">
        {/* Patient Header */}
        <Card className="border border-gray-200 shadow-sm bg-white">
          <CardHeader className="pb-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <div className="h-20 w-20 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
                  <User className="h-10 w-10 text-white" />
                </div>
                <div>
                  <CardTitle className="text-3xl font-bold text-gray-900 mb-2">
                    {patient.firstName} {patient.lastName}
                  </CardTitle>
                  <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{calculateAge(patient.dateOfBirth)} years</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <User className="h-4 w-4" />
                      <span>{formatGenderDisplay(patient.gender)}</span>
                    </div>
                    {patient.bloodGroup && (
                      <div className="flex items-center space-x-1">
                        <Heart className="h-4 w-4 text-red-500" />
                        <span className="font-semibold text-red-600">{patient.bloodGroup}</span>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-1 text-sm text-gray-600">
                      <Phone className="h-4 w-4" />
                      <span>{patient.phone}</span>
                    </div>
                    {patient.abhaProfile?.abhaNumber && (
                      <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
                        <ShieldCheck className="h-3 w-3 mr-1" />
                        ABHA: {patient.abhaProfile.abhaNumber}
                      </Badge>
                    )}
                    {patient.status === "deleted" && (
                      <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
                        <Archive className="h-3 w-3 mr-1" />
                        Archived
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/patients/${patient.id}/edit`)}
              >
                <Pencil className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MoreVertical className="h-4 w-4 mr-2" />
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {patient.status === "deleted" ? (
                    <DropdownMenuItem onClick={handleUnarchive}>
                      <Archive className="h-4 w-4 mr-2" />
                      Unarchive Patient
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem onClick={handleSoftDelete}>
                      <Archive className="h-4 w-4 mr-2" />
                      Archive Patient
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem onClick={() => router.push(`/patients/${patient.id}/consents`)}>
                    <FileText className="h-4 w-4 mr-2" />
                    Manage Consents
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleHardDelete}
                    className="text-red-600 focus:text-red-600"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Permanently
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{patient.phone}</span>
            </div>
            {patient.email && (
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{patient.email}</span>
              </div>
            )}
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                Registered {formatDate(patient.registrationDate || patient.createdAt)}
              </span>
            </div>
            {patient.primaryBranch && (
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{patient.primaryBranch.name}</span>
              </div>
            )}
            <div className="flex items-center space-x-2">
              <Badge
                variant={patient.status === "active" ? "default" : "secondary"}
                className={
                  patient.status === "active"
                    ? "bg-green-100 text-green-800"
                    : "bg-gray-100 text-gray-800"
                }
              >
                {patient.status}
              </Badge>
            </div>
            <div className="flex flex-col space-y-1">
              {patient.abhaProfile ? (
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    <ShieldCheck className="h-3 w-3 mr-1" />
                    ABHA Linked
                  </Badge>
                  {patient.abhaProfile.kycVerified && (
                    <Badge variant="outline" className="text-xs">
                      KYC ✓
                    </Badge>
                  )}
                </div>
              ) : (
                <Badge variant="outline" className="text-muted-foreground">
                  <Shield className="h-3 w-3 mr-1" />
                  No ABHA
                </Badge>
              )}
              {patient.abhaProfile?.abhaNumber && (
                <span className="text-xs text-muted-foreground font-mono">
                  {patient.abhaProfile.abhaNumber}
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

        {/* Patient Details Tabs */}
        <Card className="border border-gray-200 shadow-sm bg-white">
          <Tabs value={activeTab} onValueChange={(value) => {
            setActiveTab(value);
            router.push(`/patients/${patient.id}?tab=${value}`, { scroll: false });
          }} className="w-full">
            <TabsList className="grid w-full grid-cols-6 bg-white border-b border-gray-200 rounded-none h-12">
              <TabsTrigger
                value="personal"
                className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none font-medium"
              >
                Personal
              </TabsTrigger>
              <TabsTrigger
                value="medical"
                className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none font-medium"
              >
                Medical
              </TabsTrigger>
              <TabsTrigger
                value="consultations"
                className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none font-medium"
              >
                Consultations
              </TabsTrigger>
              <TabsTrigger
                value="emergency"
                className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none font-medium"
              >
                Emergency
              </TabsTrigger>
              <TabsTrigger
                value="insurance"
                className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none font-medium"
              >
                Insurance
              </TabsTrigger>
              <TabsTrigger
                value="abha"
                className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none font-medium"
              >
                ABHA
              </TabsTrigger>
            </TabsList>

            <div className="p-6">
              <TabsContent value="personal" className="mt-0">
                <Card className="border-0 shadow-none">
            <CardHeader>
              <CardTitle className="flex items-center text-gray-900">
                <User className="h-5 w-5 mr-2 text-blue-600" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Date of Birth</label>
                <p className="text-sm">{formatDate(patient.dateOfBirth)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Gender</label>
                <p className="text-sm">{formatGenderDisplay(patient.gender)}</p>
              </div>
              {patient.maritalStatus && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Marital Status</label>
                  <p className="text-sm">{patient.maritalStatus}</p>
                </div>
              )}
              {patient.occupation && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Occupation</label>
                  <p className="text-sm">{patient.occupation}</p>
                </div>
              )}
              {patient.address && (
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-muted-foreground">Address</label>
                  <p className="text-sm">
                    {patient.address}
                    {patient.city && `, ${patient.city}`}
                    {patient.state && `, ${patient.state}`}
                    {patient.pincode && ` - ${patient.pincode}`}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="medical" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Heart className="h-5 w-5 mr-2" />
                Medical Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!patient.allergies && !patient.chronicDiseases && !patient.currentMedications && !patient.familyMedicalHistory ? (
                <div className="text-center py-8">
                  <Heart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Medical Information</h3>
                  <p className="text-gray-500 mb-4">No medical history has been recorded for this patient yet.</p>
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/patients/${patient.id}/edit`)}
                  >
                    Add Medical Information
                  </Button>
                </div>
              ) : (
                <>
                  {patient.allergies && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Allergies</label>
                      <p className="text-sm">{patient.allergies}</p>
                    </div>
                  )}
                  {patient.chronicDiseases && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Chronic Diseases</label>
                      <p className="text-sm">{patient.chronicDiseases}</p>
                    </div>
                  )}
                  {patient.currentMedications && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Current Medications</label>
                      <p className="text-sm">{patient.currentMedications}</p>
                    </div>
                  )}
                  {patient.familyMedicalHistory && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Family Medical History</label>
                      <p className="text-sm">{patient.familyMedicalHistory}</p>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="emergency" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertCircle className="h-5 w-5 mr-2" />
                Emergency Contact
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {patient.emergencyContactName && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Contact Name</label>
                  <p className="text-sm">{patient.emergencyContactName}</p>
                </div>
              )}
              {patient.emergencyContactPhone && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Contact Phone</label>
                  <p className="text-sm">{patient.emergencyContactPhone}</p>
                </div>
              )}
              {patient.emergencyContactRelation && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Relationship</label>
                  <p className="text-sm">{patient.emergencyContactRelation}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insurance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Insurance Information
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {patient.insuranceProvider && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Insurance Provider</label>
                  <p className="text-sm">{patient.insuranceProvider}</p>
                </div>
              )}
              {patient.insurancePolicyNumber && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Policy Number</label>
                  <p className="text-sm">{patient.insurancePolicyNumber}</p>
                </div>
              )}
              {patient.insuranceExpiryDate && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Expiry Date</label>
                  <p className="text-sm">{formatDate(patient.insuranceExpiryDate)}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="abha" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <IdCard className="h-5 w-5 mr-2" />
                ABHA Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              {patient.abhaProfile ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">ABHA Number</label>
                      <p className="text-sm font-mono">{patient.abhaProfile.abhaNumber}</p>
                    </div>
                    {patient.abhaProfile.abhaAddress && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">ABHA Address</label>
                        <p className="text-sm">{patient.abhaProfile.abhaAddress}</p>
                      </div>
                    )}
                    {patient.abhaProfile.healthIdNumber && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Health ID</label>
                        <p className="text-sm font-mono">{patient.abhaProfile.healthIdNumber}</p>
                      </div>
                    )}
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">KYC Status</label>
                      <div className="mt-1">
                        <Badge
                          variant={patient.abhaProfile.kycVerified ? "default" : "secondary"}
                          className={
                            patient.abhaProfile.kycVerified
                              ? "bg-green-100 text-green-800"
                              : "bg-yellow-100 text-yellow-800"
                          }
                        >
                          {patient.abhaProfile.kycVerified ? "Verified" : "Pending"}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(`/patients/${patient.id}/abha/view`)}
                    >
                      <IdCard className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(`/patients/${patient.id}/abha/view-card`)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Card
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(`/patients/${patient.id}/consents/request`)}
                    >
                      <LinkIcon className="h-4 w-4 mr-2" />
                      Manage Consents
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(`/patients/${patient.id}/abha/update-contact`)}
                    >
                      <Phone className="h-4 w-4 mr-2" />
                      Update Contact
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <IdCard className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No ABHA Profile</h3>
                  <p className="text-gray-500 mb-4">This patient doesn't have an ABHA profile yet.</p>
                  <div className="flex justify-center space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => router.push(`/patients/${patient.id}/abha/create`)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create ABHA
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => router.push(`/patients/${patient.id}/abha/verify`)}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Verify ABHA
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="consultations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="h-5 w-5 mr-2" />
                Consultation History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ConsultationTimeline patientId={patient.id} />
            </CardContent>
          </Card>
              </TabsContent>
            </div>
          </Tabs>
        </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {deleteType === 'hard' ? 'Delete Patient Permanently' : 'Archive Patient'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {deleteType === 'hard' ? (
                <>
                  Are you sure you want to permanently delete this patient? This action cannot be undone.
                  All patient data, including consultations, appointments, and medical records will be permanently removed.
                </>
              ) : (
                <>
                  Are you sure you want to archive this patient? This will mark the patient as inactive
                  but preserve all their data. You can restore them later if needed.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeletePatient}
              disabled={isDeleting}
              className={deleteType === 'hard' ? "bg-red-600 hover:bg-red-700" : "bg-orange-600 hover:bg-orange-700"}
            >
              {isDeleting
                ? (deleteType === 'hard' ? "Deleting..." : "Archiving...")
                : (deleteType === 'hard' ? "Delete Permanently" : "Archive Patient")
              }
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      </div>
    </div>
  );
}
