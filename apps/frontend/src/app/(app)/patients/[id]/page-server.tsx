import { Suspense } from "react";
import { getCurrentUser } from "@/lib/session";
import { notFound } from "next/navigation";
import { getPatient, getPatientAppointments, getPatientConsultations } from "../actions";

import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { PatientDetailPageClient } from "./page-client-new";

interface PatientDetailPageProps {
  params: { id: string };
  searchParams: { success?: string; tab?: string };
}

// Loading component for patient details
function PatientDetailSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="h-10 w-10 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-8 w-48 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <div className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
          <div className="h-96 bg-gray-200 rounded-lg animate-pulse"></div>
        </div>
        <div className="space-y-6">
          <div className="h-48 bg-gray-200 rounded-lg animate-pulse"></div>
          <div className="h-32 bg-gray-200 rounded-lg animate-pulse"></div>
        </div>
      </div>
    </div>
  );
}

// Server component for patient data
async function PatientData({ params, searchParams }: PatientDetailPageProps) {
  try {
    // Fetch patient data in parallel
    const [patient, appointments, consultations] = await Promise.all([
      getPatient(params.id),
      getPatientAppointments(params.id).catch(() => []),
      getPatientConsultations(params.id).catch(() => []),
    ]);

    // Convert Date objects to strings for client components
    const serializedPatient = {
      ...patient,
      dateOfBirth: patient.dateOfBirth.toISOString(),
      registrationDate: patient.registrationDate?.toISOString() || patient.createdAt.toISOString(),
      createdAt: patient.createdAt.toISOString(),
      updatedAt: patient.updatedAt.toISOString(),
      insuranceExpiryDate: patient.insuranceExpiryDate?.toISOString(),
      // Convert null values to undefined for compatibility
      bloodGroup: patient.bloodGroup || undefined,
      maritalStatus: patient.maritalStatus || undefined,
      occupation: patient.occupation || undefined,
      email: patient.email || undefined,
      alternatePhone: patient.alternatePhone || undefined,
      address: patient.address || undefined,
      city: patient.city || undefined,
      state: patient.state || undefined,
      pincode: patient.pincode || undefined,
      allergies: patient.allergies || undefined,
      chronicDiseases: patient.chronicDiseases || undefined,
      currentMedications: patient.currentMedications || undefined,
      familyMedicalHistory: patient.familyMedicalHistory || undefined,
      emergencyContactName: patient.emergencyContactName || undefined,
      emergencyContactPhone: patient.emergencyContactPhone || undefined,
      emergencyContactRelation: patient.emergencyContactRelation || undefined,
      insuranceProvider: patient.insuranceProvider || undefined,
      insurancePolicyNumber: patient.insurancePolicyNumber || undefined,
      // Fix user type compatibility
      user: patient.user ? {
        id: patient.user.id,
        name: patient.user.name || "",
        email: patient.user.email || "",
      } : undefined,
      // Fix abhaProfile type compatibility
      abhaProfile: patient.abhaProfile ? {
        id: patient.abhaProfile.id,
        abhaNumber: patient.abhaProfile.abhaNumber || "",
        abhaAddress: patient.abhaProfile.abhaAddress || undefined,
        healthIdNumber: patient.abhaProfile.healthIdNumber || undefined,
        abhaStatus: patient.abhaProfile.abhaStatus || undefined,
        kycVerified: patient.abhaProfile.kycVerified || false,
      } : undefined,
    };

    return (
      <PatientDetailPageClient
        patient={serializedPatient}
        appointments={appointments}
        consultations={consultations}
        showSuccessMessage={searchParams.success === "true"}
      />
    );
  } catch (error) {
    console.error("Error loading patient:", error);
    
    if (error instanceof Error && error.message === "Patient not found") {
      notFound();
    }
    
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load patient</h3>
        <p className="text-gray-600 mb-4">There was an error loading the patient details.</p>
        <div className="flex justify-center space-x-4">
          <Button asChild variant="outline">
            <Link href="/patients">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Patients
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/patients/${params.id}`}>Try Again</Link>
          </Button>
        </div>
      </div>
    );
  }
}

// Main server component
export default async function PatientDetailPage({ params, searchParams }: PatientDetailPageProps) {
  // Verify authentication on server side
  const user = await getCurrentUser();
  if (!user) {
    notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/patients">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Patients
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Patient Details</h1>
        </div>
      </div>

      {/* Patient Data with Suspense */}
      <Suspense fallback={<PatientDetailSkeleton />}>
        <PatientData params={params} searchParams={searchParams} />
      </Suspense>
    </div>
  );
}

// Export metadata for SEO
export async function generateMetadata({ params }: { params: { id: string } }) {
  try {
    const patient = await getPatient(params.id);
    return {
      title: `${patient.firstName} ${patient.lastName} - Patient Details - AranCare`,
      description: `View and manage details for patient ${patient.firstName} ${patient.lastName}`,
    };
  } catch (error) {
    return {
      title: "Patient Details - AranCare",
      description: "View and manage patient details",
    };
  }
}
