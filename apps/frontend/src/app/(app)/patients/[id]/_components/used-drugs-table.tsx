"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@workspace/data-table/component/data-table";
import { useDataTable } from "@workspace/data-table/hooks/use-data-table";
import { DataTableToolbar } from "@workspace/data-table/component/data-table-toolbar";
import { Plus, Pill } from "lucide-react";

interface UsedDrug {
  id: string;
  brandName: string;
  genericName: string;
  strength: string;
  pack: string;
  form: string;
  manufacturer: string;
  prescribedDate: string;
  status: "active" | "discontinued" | "completed";
}

interface UsedDrugsTableProps {
  patientId: string;
  initialData?: {
    data: UsedDrug[];
    pageCount: number;
  };
}

// Mock data for demonstration
const mockDrugs: UsedDrug[] = [
  {
    id: "1",
    brandName: "Apo-Ciproflox",
    genericName: "Ciprofloxacin Hydrochloride",
    strength: "250mg",
    pack: "100",
    form: "Tab",
    manufacturer: "Apotex Industries",
    prescribedDate: "2024-01-15",
    status: "completed",
  },
  {
    id: "2",
    brandName: "Acetaminophen",
    genericName: "Acetaminophen",
    strength: "500mg",
    pack: "20",
    form: "Pill",
    manufacturer: "Perrigo Company",
    prescribedDate: "2024-01-10",
    status: "active",
  },
  {
    id: "3",
    brandName: "Amoxicillin",
    genericName: "Amoxicillin",
    strength: "250mg",
    pack: "100",
    form: "Tab",
    manufacturer: "Dr. Reddy's Laboratories",
    prescribedDate: "2024-01-05",
    status: "discontinued",
  },
  {
    id: "4",
    brandName: "Atorvastatin",
    genericName: "Atorvastatin",
    strength: "250mg",
    pack: "100",
    form: "Susp.",
    manufacturer: "Pfizer Inc.",
    prescribedDate: "2023-12-20",
    status: "active",
  },
  {
    id: "5",
    brandName: "Cyclobenzaprine",
    genericName: "Cyclobenzaprine Hydrochloride",
    strength: "250mg",
    pack: "100",
    form: "Tab",
    manufacturer: "Apotex Industries",
    prescribedDate: "2023-12-15",
    status: "completed",
  },
  {
    id: "6",
    brandName: "Lyrica",
    genericName: "Pregabalin",
    strength: "250mg",
    pack: "100",
    form: "Syrop",
    manufacturer: "Pfizer Inc.",
    prescribedDate: "2023-12-10",
    status: "active",
  },
];

const getUsedDrugsColumns = (): ColumnDef<UsedDrug>[] => [
  {
    accessorKey: "brandName",
    header: "Brand Name",
    cell: ({ row }) => (
      <div className="font-medium text-gray-900">
        {row.getValue("brandName")}
      </div>
    ),
  },
  {
    accessorKey: "genericName",
    header: "Generic Name",
    cell: ({ row }) => (
      <div className="text-gray-600">
        {row.getValue("genericName")}
      </div>
    ),
  },
  {
    accessorKey: "strength",
    header: "Strength",
    cell: ({ row }) => (
      <div className="text-gray-900 font-mono">
        {row.getValue("strength")}
      </div>
    ),
  },
  {
    accessorKey: "pack",
    header: "Pack",
    cell: ({ row }) => (
      <div className="text-gray-900">
        {row.getValue("pack")}
      </div>
    ),
  },
  {
    accessorKey: "form",
    header: "Form",
    cell: ({ row }) => (
      <div className="text-gray-900">
        {row.getValue("form")}
      </div>
    ),
  },
  {
    accessorKey: "manufacturer",
    header: "Manufacturer",
    cell: ({ row }) => (
      <div className="text-gray-600 text-sm">
        {row.getValue("manufacturer")}
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return (
        <Badge
          variant={
            status === "active"
              ? "default"
              : status === "completed"
              ? "secondary"
              : "destructive"
          }
          className={
            status === "active"
              ? "bg-green-100 text-green-800"
              : status === "completed"
              ? "bg-blue-100 text-blue-800"
              : "bg-red-100 text-red-800"
          }
        >
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </Badge>
      );
    },
  },
];

export function UsedDrugsTable({ initialData }: UsedDrugsTableProps) {
  // Use mock data for now - in real implementation, this would come from props or API
  const data = initialData?.data || mockDrugs;
  const pageCount = initialData?.pageCount || Math.ceil(mockDrugs.length / 10);

  const columns = React.useMemo(() => getUsedDrugsColumns(), []);

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
  });

  if (data.length === 0) {
    return (
      <div className="text-center py-12">
        <Pill className="h-12 w-12 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Used Drugs</h3>
        <p className="text-gray-500 mb-4">No medications have been prescribed to this patient yet.</p>
        <Button variant="outline">
          <Plus className="h-4 w-4 mr-2" />
          Add Drug
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Used Drugs</h3>
          <p className="text-sm text-gray-500">Medications prescribed to this patient</p>
        </div>
        <Button variant="outline" size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add Drug
        </Button>
      </div>
      
      <DataTable
        table={table}
        doctype="usedDrugs"
      >
        <DataTableToolbar table={table} />
      </DataTable>
    </div>
  );
}
