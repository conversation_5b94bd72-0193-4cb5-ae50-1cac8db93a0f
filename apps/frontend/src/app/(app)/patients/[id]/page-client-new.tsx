"use client";

import React, { useState, useEffect } from "react";
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  Phone,
  IdCard,
  Link as LinkIcon,
  Trash2,
  Mail,
  Archive,
  MoreVertical,
  FileText,
  Edit,
} from "lucide-react";
import { formatDate } from "@/lib/utils";
import { formatGenderDisplay } from "@/lib/gender-utils";
import { toast } from "sonner";
import { ConsultationTimeline } from "@/components/consultations/consultation-timeline";
import { deletePatient } from "../actions";

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  phone: string;
  email?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  bloodGroup?: string;
  allergies?: string;
  chronicDiseases?: string;
  currentMedications?: string;
  familyMedicalHistory?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelation?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  insuranceExpiryDate?: string;
  maritalStatus?: string;
  occupation?: string;
  status: string;
  registrationDate?: string;
  primaryBranch?: {
    id: string;
    name: string;
  };
  abhaProfile?: {
    id: string;
    abhaNumber: string;
    abhaAddress?: string;
    healthIdNumber?: string;
    abhaStatus?: string;
    kycVerified: boolean;
  };
  documents?: any[];
  createdAt: string;
  updatedAt: string;
}

interface PatientDetailPageClientProps {
  patient: Patient;
  appointments: any[];
  consultations: any[];
  showSuccessMessage?: boolean;
}

// Helper function to calculate age
const calculateAge = (dateOfBirth: string): number => {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
};

export function PatientDetailPageClient({
  patient,
  appointments: _appointments,
  consultations: _consultations,
  showSuccessMessage = false,
}: PatientDetailPageClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams?.get('tab') || "personal");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteType, setDeleteType] = useState<'soft' | 'hard'>('soft');
  const [isDeleting, setIsDeleting] = useState(false);

  // Show success message if redirected after creation/update
  useEffect(() => {
    if (showSuccessMessage) {
      toast.success("Patient information updated successfully!");
    }
  }, [showSuccessMessage]);

  const handleSoftDelete = () => {
    setDeleteType('soft');
    setShowDeleteDialog(true);
  };

  const handleHardDelete = () => {
    setDeleteType('hard');
    setShowDeleteDialog(true);
  };

  const handleDeletePatient = async () => {
    setIsDeleting(true);
    try {
      const result = await deletePatient(patient.id, deleteType === 'hard');

      if (result.success) {
        toast.success(
          deleteType === 'hard'
            ? "Patient deleted permanently"
            : "Patient archived successfully"
        );
        router.push("/patients");
      }
    } catch (error) {
      console.error("Error deleting patient:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete patient");
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50/30">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-4 lg:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 lg:space-x-4">
              <h1 className="text-lg lg:text-2xl font-semibold text-gray-900 truncate">
                {patient.firstName} {patient.lastName}
              </h1>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/patients/${patient.id}/edit`)}
                className="hidden lg:flex"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Patient
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => router.push(`/patients/${patient.id}/edit`)} className="lg:hidden">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Patient
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => router.push(`/patients/${patient.id}/consents`)}>
                    <FileText className="h-4 w-4 mr-2" />
                    Manage Consents
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  {patient.status === "deleted" ? (
                    <DropdownMenuItem onClick={() => {}}>
                      <Archive className="h-4 w-4 mr-2" />
                      Unarchive Patient
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem onClick={handleSoftDelete}>
                      <Archive className="h-4 w-4 mr-2" />
                      Archive Patient
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    onClick={handleHardDelete}
                    className="text-red-600 focus:text-red-600"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Permanently
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex flex-col lg:flex-row">
          {/* Left Sidebar */}
          <div className="w-full lg:w-80 bg-white border-r border-gray-200 lg:min-h-screen">
            <div className="p-6">
              {/* Patient Avatar and Basic Info */}
              <div className="text-center mb-6">
                <Avatar className="w-24 h-24 mx-auto mb-4">
                  <AvatarImage src="" alt={`${patient.firstName} ${patient.lastName}`} />
                  <AvatarFallback className="text-2xl bg-blue-100 text-blue-600">
                    {patient.firstName.charAt(0)}{patient.lastName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <h2 className="text-xl font-semibold text-gray-900 mb-1">
                  {patient.firstName} {patient.lastName}
                </h2>
                <p className="text-sm text-gray-500 mb-2">
                  {patient.status === "active" ? "Active" : "Inactive"}
                </p>
                <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                  <span>{formatGenderDisplay(patient.gender)}</span>
                  <span>•</span>
                  <span>{calculateAge(patient.dateOfBirth)} years</span>
                </div>
              </div>

              <Separator className="mb-6" />

              {/* Personal Details */}
              <div className="space-y-4 mb-6">
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wide">
                  Personal Details
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Last name
                    </label>
                    <p className="text-sm text-gray-900">{patient.lastName}</p>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      First name
                    </label>
                    <p className="text-sm text-gray-900">{patient.firstName}</p>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Birthdate
                    </label>
                    <p className="text-sm text-gray-900">{formatDate(patient.dateOfBirth)}</p>
                  </div>
                </div>
              </div>

              <Separator className="mb-6" />

              {/* Contact Information */}
              <div className="space-y-4 mb-6">
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wide">
                  Contact Information
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-900">{patient.phone}</span>
                  </div>
                  {patient.email && (
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-900">{patient.email}</span>
                    </div>
                  )}
                </div>
              </div>

              <Separator className="mb-6" />

              {/* Patient Details */}
              <div className="space-y-4 mb-6">
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wide">
                  Patient Details
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Patient ID
                    </label>
                    <p className="text-sm text-gray-900 font-mono">{patient.id}</p>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Registration Date
                    </label>
                    <p className="text-sm text-gray-900">
                      {patient.registrationDate ? formatDate(patient.registrationDate) : formatDate(patient.createdAt)}
                    </p>
                  </div>
                  {patient.primaryBranch && (
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                        Primary Branch
                      </label>
                      <p className="text-sm text-gray-900">{patient.primaryBranch.name}</p>
                    </div>
                  )}
                  {patient.bloodGroup && (
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                        Blood Group
                      </label>
                      <p className="text-sm text-gray-900 font-semibold text-red-600">{patient.bloodGroup}</p>
                    </div>
                  )}
                  {patient.address && (
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                        Address
                      </label>
                      <p className="text-sm text-gray-900">
                        {patient.address}
                        {patient.city && `, ${patient.city}`}
                        {patient.state && `, ${patient.state}`}
                        {patient.pincode && ` - ${patient.pincode}`}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <Separator className="mb-6" />

              {/* ABHA Information */}
              {patient.abhaProfile && (
                <>
                  <div className="space-y-4 mb-6">
                    <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wide">
                      ABHA Information
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          ABHA Number
                        </label>
                        <p className="text-sm text-gray-900 font-mono">{patient.abhaProfile.abhaNumber}</p>
                      </div>
                      {patient.abhaProfile.abhaAddress && (
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                            ABHA Address
                          </label>
                          <p className="text-sm text-gray-900">{patient.abhaProfile.abhaAddress}</p>
                        </div>
                      )}
                      {patient.abhaProfile.healthIdNumber && (
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                            Health ID
                          </label>
                          <p className="text-sm text-gray-900 font-mono">{patient.abhaProfile.healthIdNumber}</p>
                        </div>
                      )}
                      <div>
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          KYC Status
                        </label>
                        <div className="mt-1">
                          <Badge
                            variant={patient.abhaProfile.kycVerified ? "default" : "secondary"}
                            className={
                              patient.abhaProfile.kycVerified
                                ? "bg-green-100 text-green-800"
                                : "bg-yellow-100 text-yellow-800"
                            }
                          >
                            {patient.abhaProfile.kycVerified ? "Verified" : "Pending"}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                  <Separator className="mb-6" />
                </>
              )}

              {/* Allergies */}
              {patient.allergies && (
                <>
                  <div className="space-y-4 mb-6">
                    <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wide">
                      Allergies
                    </h3>
                    <div className="space-y-2">
                      {patient.allergies.split(',').map((allergy, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="text-sm text-gray-900">{allergy.trim()}</span>
                          {/* <Badge variant="destructive" className="text-xs">
                            High
                          </Badge> */}
                        </div>
                      ))}
                    </div>
                    {/* <Button variant="ghost" size="sm" className="w-full justify-start text-blue-600">
                      <Plus className="h-4 w-4 mr-2" />
                      Add allergy
                    </Button> */}
                  </div>
                  <Separator className="mb-6" />
                </>
              )}

              {/* Emergency Contact */}
              {patient.emergencyContactName && (
                <>
                  <div className="space-y-4 mb-6">
                    <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wide">
                      Emergency Contact
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          Contact Name
                        </label>
                        <p className="text-sm text-gray-900">{patient.emergencyContactName}</p>
                      </div>
                      {patient.emergencyContactPhone && (
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                            Contact Phone
                          </label>
                          <p className="text-sm text-gray-900">{patient.emergencyContactPhone}</p>
                        </div>
                      )}
                      {patient.emergencyContactRelation && (
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                            Relationship
                          </label>
                          <p className="text-sm text-gray-900">{patient.emergencyContactRelation}</p>
                        </div>
                      )}
                    </div>
                  </div>
                  <Separator className="mb-6" />
                </>
              )}

              {/* Medical Information Summary */}
              {(patient.chronicDiseases || patient.currentMedications) && (
                <>
                  <div className="space-y-4 mb-6">
                    <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wide">
                      Medical Summary
                    </h3>
                    <div className="space-y-3">
                      {patient.chronicDiseases && (
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                            Chronic Diseases
                          </label>
                          <p className="text-sm text-gray-900">{patient.chronicDiseases}</p>
                        </div>
                      )}
                      {patient.currentMedications && (
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                            Current Medications
                          </label>
                          <p className="text-sm text-gray-900">{patient.currentMedications}</p>
                        </div>
                      )}
                    </div>
                  </div>
                  <Separator className="mb-6" />
                </>
              )}

              {/* Notes */}
              <div className="space-y-4">
                {/* <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wide">
                  Notes
                </h3> */}
                <div className="space-y-3">
                  {patient.familyMedicalHistory ? (
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                        Family Medical History
                      </label>
                      <p className="text-sm text-gray-900">{patient.familyMedicalHistory}</p>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 italic">No family medical history recorded</p>
                  )}

                  {/* Additional notes section */}
                  {/* <div className="mt-4">
                    <Button variant="ghost" size="sm" className="w-full justify-start text-blue-600">
                      <Plus className="h-4 w-4 mr-2" />
                      Add note
                    </Button>
                  </div> */}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="flex-1 bg-gray-50/30 min-h-screen">
            <div className="p-4 lg:p-6">
              <Tabs value={activeTab} onValueChange={(value) => {
                setActiveTab(value);
                router.push(`/patients/${patient.id}?tab=${value}`, { scroll: false });
              }} className="w-full">
                <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6 bg-white border-b border-gray-200 rounded-none h-12">
                  <TabsTrigger
                    value="personal"
                    className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none font-medium"
                  >
                    Personal
                  </TabsTrigger>
                  <TabsTrigger
                    value="medical"
                    className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none font-medium"
                  >
                    Medical
                  </TabsTrigger>
                  <TabsTrigger
                    value="consultations"
                    className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none font-medium"
                  >
                    Consultations
                  </TabsTrigger>
                  <TabsTrigger
                    value="emergency"
                    className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none font-medium"
                  >
                    Emergency
                  </TabsTrigger>
                  <TabsTrigger
                    value="insurance"
                    className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none font-medium"
                  >
                    Insurance
                  </TabsTrigger>
                  <TabsTrigger
                    value="abha"
                    className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none font-medium"
                  >
                    ABHA
                  </TabsTrigger>
                </TabsList>

                {/* Personal Tab */}
                <TabsContent value="personal" className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Personal Information */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Personal Information</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-gray-500">Date of Birth</label>
                            <p className="text-sm text-gray-900">{formatDate(patient.dateOfBirth)}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500">Gender</label>
                            <p className="text-sm text-gray-900">{formatGenderDisplay(patient.gender)}</p>
                          </div>
                          {patient.maritalStatus && (
                            <div>
                              <label className="text-sm font-medium text-gray-500">Marital Status</label>
                              <p className="text-sm text-gray-900">{patient.maritalStatus}</p>
                            </div>
                          )}
                          {patient.occupation && (
                            <div>
                              <label className="text-sm font-medium text-gray-500">Occupation</label>
                              <p className="text-sm text-gray-900">{patient.occupation}</p>
                            </div>
                          )}
                        </div>
                        {patient.address && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Address</label>
                            <p className="text-sm text-gray-900">
                              {patient.address}
                              {patient.city && `, ${patient.city}`}
                              {patient.state && `, ${patient.state}`}
                              {patient.pincode && ` - ${patient.pincode}`}
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Medical Information */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Medical Information</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {patient.bloodGroup && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Blood Group</label>
                            <p className="text-sm text-gray-900 font-semibold text-red-600">{patient.bloodGroup}</p>
                          </div>
                        )}
                        {patient.allergies && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Allergies</label>
                            <p className="text-sm text-gray-900">{patient.allergies}</p>
                          </div>
                        )}
                        {patient.chronicDiseases && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Chronic Diseases</label>
                            <p className="text-sm text-gray-900">{patient.chronicDiseases}</p>
                          </div>
                        )}
                        {patient.currentMedications && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Current Medications</label>
                            <p className="text-sm text-gray-900">{patient.currentMedications}</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Emergency Contact */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Emergency Contact</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {patient.emergencyContactName ? (
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="text-sm font-medium text-gray-500">Contact Name</label>
                              <p className="text-sm text-gray-900">{patient.emergencyContactName}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-500">Contact Phone</label>
                              <p className="text-sm text-gray-900">{patient.emergencyContactPhone}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-500">Relationship</label>
                              <p className="text-sm text-gray-900">{patient.emergencyContactRelation}</p>
                            </div>
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500">No emergency contact information available</p>
                        )}
                      </CardContent>
                    </Card>

                    {/* Insurance Information */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Insurance Information</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {patient.insuranceProvider ? (
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="text-sm font-medium text-gray-500">Insurance Provider</label>
                              <p className="text-sm text-gray-900">{patient.insuranceProvider}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-500">Policy Number</label>
                              <p className="text-sm text-gray-900">{patient.insurancePolicyNumber}</p>
                            </div>
                            {patient.insuranceExpiryDate && (
                              <div>
                                <label className="text-sm font-medium text-gray-500">Expiry Date</label>
                                <p className="text-sm text-gray-900">{formatDate(patient.insuranceExpiryDate)}</p>
                              </div>
                            )}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500">No insurance information available</p>
                        )}
                      </CardContent>
                    </Card>
                  </div>

                  {/* ABHA Information */}
                  {patient.abhaProfile && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center">
                          <IdCard className="h-5 w-5 mr-2" />
                          ABHA Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div>
                            <label className="text-sm font-medium text-gray-500">ABHA Number</label>
                            <p className="text-sm font-mono text-gray-900">{patient.abhaProfile.abhaNumber}</p>
                          </div>
                          {patient.abhaProfile.abhaAddress && (
                            <div>
                              <label className="text-sm font-medium text-gray-500">ABHA Address</label>
                              <p className="text-sm text-gray-900">{patient.abhaProfile.abhaAddress}</p>
                            </div>
                          )}
                          <div>
                            <label className="text-sm font-medium text-gray-500">KYC Status</label>
                            <Badge
                              variant={patient.abhaProfile.kycVerified ? "default" : "secondary"}
                              className={
                                patient.abhaProfile.kycVerified
                                  ? "bg-green-100 text-green-800"
                                  : "bg-yellow-100 text-yellow-800"
                              }
                            >
                              {patient.abhaProfile.kycVerified ? "Verified" : "Pending"}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/patients/${patient.id}/abha/view`)}
                          >
                            <IdCard className="h-4 w-4 mr-2" />
                            View Details
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/patients/${patient.id}/consents/request`)}
                          >
                            <LinkIcon className="h-4 w-4 mr-2" />
                            Manage Consents
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                {/* Medical Tab */}
                <TabsContent value="medical" className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
                    {/* Medical Information */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Medical Information</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {patient.bloodGroup && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Blood Group</label>
                            <p className="text-sm text-gray-900 font-semibold text-red-600">{patient.bloodGroup}</p>
                          </div>
                        )}
                        {patient.allergies && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Allergies</label>
                            <p className="text-sm text-gray-900">{patient.allergies}</p>
                          </div>
                        )}
                        {patient.chronicDiseases && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Chronic Diseases</label>
                            <p className="text-sm text-gray-900">{patient.chronicDiseases}</p>
                          </div>
                        )}
                        {patient.currentMedications && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Current Medications</label>
                            <p className="text-sm text-gray-900">{patient.currentMedications}</p>
                          </div>
                        )}
                        {patient.familyMedicalHistory && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Family Medical History</label>
                            <p className="text-sm text-gray-900">{patient.familyMedicalHistory}</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Used Drugs Table */}
                    {/* <Card>
                      <CardContent className="p-6">
                        <UsedDrugsTable patientId={patient.id} />
                      </CardContent>
                    </Card> */}
                  </div>
                </TabsContent>

                {/* Consultations Tab */}
                <TabsContent value="consultations" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Consultation History</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ConsultationTimeline patientId={patient.id} />
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Emergency Tab */}
                <TabsContent value="emergency" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Emergency Contact</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {patient.emergencyContactName ? (
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-gray-500">Contact Name</label>
                            <p className="text-sm text-gray-900">{patient.emergencyContactName}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500">Contact Phone</label>
                            <p className="text-sm text-gray-900">{patient.emergencyContactPhone}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500">Relationship</label>
                            <p className="text-sm text-gray-900">{patient.emergencyContactRelation}</p>
                          </div>
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500">No emergency contact information available</p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Insurance Tab */}
                <TabsContent value="insurance" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Insurance Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {patient.insuranceProvider ? (
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-gray-500">Insurance Provider</label>
                            <p className="text-sm text-gray-900">{patient.insuranceProvider}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500">Policy Number</label>
                            <p className="text-sm text-gray-900">{patient.insurancePolicyNumber}</p>
                          </div>
                          {patient.insuranceExpiryDate && (
                            <div>
                              <label className="text-sm font-medium text-gray-500">Expiry Date</label>
                              <p className="text-sm text-gray-900">{formatDate(patient.insuranceExpiryDate)}</p>
                            </div>
                          )}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500">No insurance information available</p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* ABHA Tab */}
                <TabsContent value="abha" className="space-y-6">
                  {patient.abhaProfile ? (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center">
                          <IdCard className="h-5 w-5 mr-2" />
                          ABHA Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div>
                            <label className="text-sm font-medium text-gray-500">ABHA Number</label>
                            <p className="text-sm font-mono text-gray-900">{patient.abhaProfile.abhaNumber}</p>
                          </div>
                          {patient.abhaProfile.abhaAddress && (
                            <div>
                              <label className="text-sm font-medium text-gray-500">ABHA Address</label>
                              <p className="text-sm text-gray-900">{patient.abhaProfile.abhaAddress}</p>
                            </div>
                          )}
                          <div>
                            <label className="text-sm font-medium text-gray-500">KYC Status</label>
                            <Badge
                              variant={patient.abhaProfile.kycVerified ? "default" : "secondary"}
                              className={
                                patient.abhaProfile.kycVerified
                                  ? "bg-green-100 text-green-800"
                                  : "bg-yellow-100 text-yellow-800"
                              }
                            >
                              {patient.abhaProfile.kycVerified ? "Verified" : "Pending"}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/patients/${patient.id}/abha/view`)}
                          >
                            <IdCard className="h-4 w-4 mr-2" />
                            View Details
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/patients/${patient.id}/consents/request`)}
                          >
                            <LinkIcon className="h-4 w-4 mr-2" />
                            Manage Consents
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">ABHA Information</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-500">No ABHA information available for this patient.</p>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>


              </Tabs>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {deleteType === 'hard' ? 'Delete Patient Permanently' : 'Archive Patient'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {deleteType === 'hard' ? (
                <>
                  Are you sure you want to permanently delete this patient? This action cannot be undone.
                  All patient data, including consultations, appointments, and medical records will be permanently removed.
                </>
              ) : (
                <>
                  Are you sure you want to archive this patient? This will mark the patient as inactive
                  but preserve all their data. You can restore them later if needed.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeletePatient}
              disabled={isDeleting}
              className={deleteType === 'hard' ? "bg-red-600 hover:bg-red-700" : "bg-orange-600 hover:bg-orange-700"}
            >
              {isDeleting
                ? (deleteType === 'hard' ? "Deleting..." : "Archiving...")
                : (deleteType === 'hard' ? "Delete Permanently" : "Archive Patient")
              }
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
