"use client";

import { useState, useRef, useTran<PERSON>tion, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { usePatientSearchWithABHA } from "@/hooks/use-patient-search-with-abha";
import { isP<PERSON><PERSON><PERSON><PERSON>, is<PERSON>bha<PERSON>ddress } from "@/services/patient-service";
import { searchPatients } from "./actions";
import { formatGenderDisplay } from "@/lib/gender-utils";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PatientSearchBanner } from "@/components/patient-search-banner";
import { PatientsEmptyState } from "@/components/ui/empty-state";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Eye,
  Edit,
  ShieldCheck,
  ShieldX,
  Phone,
  Mail,
  User,
  Activity,
} from "lucide-react";

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  bloodGroup?: string;
  maritalStatus?: string;
  occupation?: string;
  email?: string;
  phone: string;
  alternatePhone?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country: string;
  allergies?: string;
  chronicDiseases?: string;
  currentMedications?: string;
  familyMedicalHistory?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelation?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  insuranceExpiryDate?: string;
  registrationDate: string;
  status: string;
  primaryBranch?: {
    id: string;
    name: string;
  };
  user?: {
    id: string;
    name: string;
    email: string;
  };
  abhaProfile?: {
    id: string;
    abhaNumber: string;
    abhaAddress?: string;
    healthIdNumber?: string;
    abhaStatus?: string;
    kycVerified: boolean;
  };
  linkTokenRequests?: any[];
  abhaLinkTokens?: any[];
  createdAt: string;
  updatedAt: string;
}

interface Pagination {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface PatientsPageClientProps {
  initialPatients: Patient[];
  initialPagination: Pagination;
  initialFilters: {
    status?: string;
    search?: string;
    page?: number;
    limit?: number;
  };
}

export function PatientsPageClient({
  initialPatients,
  initialPagination,
  initialFilters,
}: PatientsPageClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  
  // State
  const [patients, setPatients] = useState<Patient[]>(initialPatients);
  const [pagination, setPagination] = useState<Pagination>(initialPagination);
  const [searchQuery, setSearchQuery] = useState(initialFilters.search || "");
  const [statusFilter, setStatusFilter] = useState(initialFilters.status || "active");
  const [isLoading, setIsLoading] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Enhanced search with ABHA integration
  const {
    bannerStates,
    isSearching,
    searchPhoneWithABHA,
    clearSearch,
    hideBanner,
  } = usePatientSearchWithABHA();

  // Sync search query with URL parameters
  useEffect(() => {
    const urlSearch = searchParams?.get('search') || '';
    if (urlSearch !== searchQuery) {
      setSearchQuery(urlSearch);
    }
  }, [searchParams]);

  // Fetch patients function using server action
  const fetchPatients = async (filters: { search?: string; status?: string; page?: number }) => {
    setIsLoading(true);
    try {
      const result = await searchPatients({
        search: filters.search || "",
        status: filters.status || "active",
        page: filters.page || 1,
        limit: 50
      });

      if (result.success) {
        setPatients(result.data as Patient[]);
        setPagination(result.pagination);
      } else {
        console.error("Failed to fetch patients:", result.error);
        setPatients([]);
      }
    } catch (error) {
      console.error("Error fetching patients:", error);
      setPatients([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Update URL and fetch data
  const updateFilters = (newFilters: Partial<typeof initialFilters>) => {
    const params = new URLSearchParams(searchParams || undefined);

    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && value !== "all" && value !== "") {
        params.set(key, value.toString());
      } else {
        params.delete(key);
      }
    });

    // Reset to page 1 when filters change (except when changing page)
    if (!newFilters.page) {
      params.delete("page");
    }

    // Update local state immediately
    if (newFilters.search !== undefined) {
      setSearchQuery(newFilters.search);
    }
    if (newFilters.status !== undefined) {
      setStatusFilter(newFilters.status);
    }

    // Fetch new data
    fetchPatients({
      search: newFilters.search || searchQuery,
      status: newFilters.status || statusFilter,
      page: 1
    });

    startTransition(() => {
      router.push(`/patients?${params.toString()}`);
    });
  };

  // Handle search input
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);

    // Clear existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Clear any existing ABHA search results when switching to regular search
    if (!isPhoneNumber(value) && !isAbhaAddress(value)) {
      clearSearch();
    }

    // Set new timeout for search
    searchTimeoutRef.current = setTimeout(() => {
      if (value.trim() === "") {
        // Empty search - show all records
        clearSearch();
        updateFilters({ search: "" });
      } else if (isPhoneNumber(value) || isAbhaAddress(value)) {
        // Enhanced search for phone/ABHA
        searchPhoneWithABHA(value);
      } else {
        // Regular search
        updateFilters({ search: value });
      }
    }, 300); // Reduced timeout for better responsiveness
  };

  // Handle status filter change
  const handleStatusChange = (status: string) => {
    setStatusFilter(status);
    updateFilters({ status });
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    updateFilters({ page });
  };

  // Handle link/delink ABHA
  const handleLinkAbha = (patientId: string, hasAbha: boolean) => {
    if (hasAbha) {
      router.push(`/patients/${patientId}/abha/view`);
    } else {
      router.push(`/patients/${patientId}/abha/verify`);
    }
  };

  // Calculate age
  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };



  return (
    <Card className="border-0 shadow-md rounded-xl overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-primary/5 via-background to-secondary/5">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <CardTitle>Patient Management</CardTitle>
            <CardDescription>
              View and manage all patients in the system
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        {/* Search Banner for ABHA Integration */}
        {bannerStates.length > 0 && (
          <div className="mb-6 space-y-4">
            {bannerStates.map((banner, index) =>
              banner.show ? (
                <div key={`${banner.type}-${index}`} className="mb-4">
                  <PatientSearchBanner
                    banner={banner}
                    onHide={() => hideBanner(index)}
                  />
                </div>
              ) : null,
            )}
          </div>
        )}

        {/* Search and Filter Controls */}
        <div className="bg-white rounded-lg border p-4 shadow-sm mb-6">
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
            <div className="flex-1 max-w-xl">
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search patients..."
                  className="pl-10 h-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  disabled={isSearching || isPending || isLoading}
                />
                {(isSearching || isPending || isLoading) && (
                  <div className="absolute right-3 top-2.5">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={handleStatusChange}>
                <SelectTrigger className="w-[160px] h-10">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active Patients</SelectItem>
                  <SelectItem value="inactive">Inactive Patients</SelectItem>
                  <SelectItem value="deleted">Archived Patients</SelectItem>
                  <SelectItem value="all">All Patients</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Patients Table */}
        <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="font-medium">Patient</TableHead>
                <TableHead className="font-medium">Contact</TableHead>
                <TableHead className="font-medium">Age/Gender</TableHead>
                <TableHead className="font-medium">ABHA Status</TableHead>
                <TableHead className="font-medium">Branch</TableHead>
                <TableHead className="font-medium">Status</TableHead>
                <TableHead className="font-medium text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {patients.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="p-0">
                    <PatientsEmptyState
                      searchQuery={searchQuery}
                      statusFilter={statusFilter}
                      onCreatePatient={() => router.push("/patients/new")}
                      onClearFilters={() => {
                        setSearchQuery("");
                        setStatusFilter("active");
                        clearSearch();
                        updateFilters({ search: "", status: "active" });
                      }}
                    />
                  </TableCell>
                </TableRow>
              ) : (
                patients.map((patient) => (
                  <TableRow key={patient.id} className="hover:bg-gray-50">
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <User className="h-5 w-5 text-blue-600" />
                          </div>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">
                            {patient.firstName} {patient.lastName}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {patient.id.slice(-8)}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm">
                          <Phone className="h-3 w-3 mr-1" />
                          {patient.phone}
                        </div>
                        {patient.email && (
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Mail className="h-3 w-3 mr-1" />
                            {patient.email}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-sm">
                          {calculateAge(patient.dateOfBirth)} years
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {formatGenderDisplay(patient.gender)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {patient.abhaProfile ? (
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                              <ShieldCheck className="h-3 w-3 mr-1" />
                              Linked
                            </Badge>
                            {patient.abhaProfile.kycVerified && (
                              <Badge variant="outline" className="text-xs">
                                KYC ✓
                              </Badge>
                            )}
                          </div>
                          {patient.abhaProfile.abhaNumber && (
                            <div className="text-xs text-muted-foreground font-mono">
                              ABHA: {patient.abhaProfile.abhaNumber}
                            </div>
                          )}
                          {patient.abhaProfile.abhaAddress && (
                            <div className="text-xs text-muted-foreground">
                              {patient.abhaProfile.abhaAddress}
                            </div>
                          )}
                        </div>
                      ) : (
                        <Badge variant="outline" className="text-muted-foreground">
                          <ShieldX className="h-3 w-3 mr-1" />
                          Not Linked
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {patient.primaryBranch?.name || "N/A"}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={patient.status === "active" ? "default" : "secondary"}
                        className={
                          patient.status === "active"
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }
                      >
                        {patient.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => router.push(`/patients/${patient.id}`)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => router.push(`/patients/${patient.id}/edit`)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleLinkAbha(patient.id, !!patient.abhaProfile)}
                        >
                          <Activity className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-6">
            <div className="text-sm text-muted-foreground">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
              {Math.min(pagination.page * pagination.limit, pagination.totalCount)} of{" "}
              {pagination.totalCount} patients
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={!pagination.hasPrevPage || isPending}
              >
                Previous
              </Button>
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={page === pagination.page ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(page)}
                      disabled={isPending}
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={!pagination.hasNextPage || isPending}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
