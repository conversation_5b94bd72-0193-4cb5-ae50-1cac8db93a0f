import { Suspense } from "react";
import { getCurrentUser } from "@/lib/session";
import { notFound } from "next/navigation";
import { getPatients } from "./actions";


import { Button } from "@/components/ui/button";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { PatientsPageClient } from "./page-client";

interface PatientsPageProps {
  searchParams: {
    status?: string;
    search?: string;
    page?: string;
    limit?: string;
  };
}

// Loading component for patients table
function PatientsTableSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="h-8 w-48 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
      </div>
      <div className="border rounded-lg">
        <div className="p-4 border-b">
          <div className="h-6 w-64 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="p-6 space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-4">
              <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 w-28 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Server component for patients data
async function PatientsData({ searchParams }: PatientsPageProps) {
  try {
    const filters = {
      status: searchParams.status || "active", // Default to active patients only
      search: searchParams.search,
      page: searchParams.page ? parseInt(searchParams.page) : 1,
      limit: searchParams.limit ? parseInt(searchParams.limit) : 50,
    };

    const { patients, pagination } = await getPatients(filters);

    // Convert Date objects to strings for client components
    const serializedPatients = patients.map(patient => ({
      ...patient,
      dateOfBirth: patient.dateOfBirth.toISOString(),
      registrationDate: patient.registrationDate?.toISOString() || patient.createdAt.toISOString(),
      createdAt: patient.createdAt.toISOString(),
      updatedAt: patient.updatedAt.toISOString(),
      insuranceExpiryDate: patient.insuranceExpiryDate?.toISOString(),
      // Convert null values to undefined for compatibility
      bloodGroup: patient.bloodGroup || undefined,
      maritalStatus: patient.maritalStatus || undefined,
      occupation: patient.occupation || undefined,
      email: patient.email || undefined,
      alternatePhone: patient.alternatePhone || undefined,
      address: patient.address || undefined,
      city: patient.city || undefined,
      state: patient.state || undefined,
      pincode: patient.pincode || undefined,
      allergies: patient.allergies || undefined,
      chronicDiseases: patient.chronicDiseases || undefined,
      currentMedications: patient.currentMedications || undefined,
      familyMedicalHistory: patient.familyMedicalHistory || undefined,
      emergencyContactName: patient.emergencyContactName || undefined,
      emergencyContactPhone: patient.emergencyContactPhone || undefined,
      emergencyContactRelation: patient.emergencyContactRelation || undefined,
      insuranceProvider: patient.insuranceProvider || undefined,
      insurancePolicyNumber: patient.insurancePolicyNumber || undefined,
      // Fix user type compatibility
      user: patient.user ? {
        id: patient.user.id,
        name: patient.user.name || "",
        email: patient.user.email || "",
      } : undefined,
      // Fix abhaProfile type compatibility
      abhaProfile: patient.abhaProfile ? {
        id: patient.abhaProfile.id,
        abhaNumber: patient.abhaProfile.abhaNumber || "",
        abhaAddress: patient.abhaProfile.abhaAddress || undefined,
        healthIdNumber: patient.abhaProfile.healthIdNumber || undefined,
        abhaStatus: patient.abhaProfile.abhaStatus || undefined,
        kycVerified: patient.abhaProfile.kycVerified || false,
      } : undefined,
    }));

    return (
      <PatientsPageClient
        initialPatients={serializedPatients}
        initialPagination={pagination}
        initialFilters={filters}
      />
    );
  } catch (error) {
    console.error("Error loading patients:", error);
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load patients</h3>
        <p className="text-gray-600 mb-4">There was an error loading the patients list.</p>
        <Button asChild>
          <Link href="/patients">Try Again</Link>
        </Button>
      </div>
    );
  }
}

// Main server component
export default async function PatientsPage({ searchParams }: PatientsPageProps) {
  // Verify authentication on server side
  const user = await getCurrentUser();
  if (!user) {
    notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Patients</h1>
        <div className="flex space-x-2">
          <Button asChild>
            <Link href="/patients/register">
              <PlusIcon className="mr-2 h-4 w-4" />
              Register Patient
            </Link>
          </Button>
        </div>
      </div>

      {/* Patients Data with Suspense */}
      <Suspense fallback={<PatientsTableSkeleton />}>
        <PatientsData searchParams={searchParams} />
      </Suspense>
    </div>
  );
}

// Export metadata for SEO
export const metadata = {
  title: "Patients - AranCare",
  description: "Manage and view all patients in the healthcare system",
};
