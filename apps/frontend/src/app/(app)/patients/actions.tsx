"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/session";
import { cookies } from "next/headers";

// Types
interface PatientFormData {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  bloodGroup?: string;
  maritalStatus?: string;
  occupation?: string;
  email?: string;
  phone: string;
  communicationMobile?: string;
  alternatePhone?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country?: string;
  allergies?: string;
  chronicDiseases?: string;
  currentMedications?: string;
  familyMedicalHistory?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelation?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  insuranceExpiryDate?: string;
  status?: string;
  primaryBranchId?: string;
  // ABHA related fields
  abhaNumber?: string;
  abhaAddress?: string;
  healthIdNumber?: string;
  abhaStatus?: string;
  kycVerified?: boolean;
  fromAbhaVerification?: boolean | string;
}

interface PatientSearchFilters {
  status?: string;
  search?: string;
  page?: number;
  limit?: number;
}

// Helper function to get organization context
async function getOrganizationContext() {
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  const userInfoCookie = cookies().get("user-info")?.value;
  let organizationId = "";

  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(userInfoCookie);
      organizationId = userInfo.organizationId || "";
    } catch (error) {
      console.error("Error parsing user info:", error);
    }
  }

  if (!organizationId) {
    throw new Error("No organization found");
  }

  return { user, organizationId };
}

// Helper function to get branch context
async function getBranchContext(providedBranchId?: string) {
  const { user, organizationId } = await getOrganizationContext();
  
  let branchId = providedBranchId;

  if (!branchId) {
    // Try to get from cookies
    const defaultBranchCookie = cookies().get("default-branch")?.value;
    if (defaultBranchCookie) {
      try {
        const branchInfo = JSON.parse(defaultBranchCookie);
        branchId = branchInfo.id;
      } catch (error) {
        console.error("Error parsing default branch cookie:", error);
      }
    }

    // Fallback to current branch cookie
    if (!branchId) {
      const currentBranchCookie = cookies().get("current-branch")?.value;
      if (currentBranchCookie) {
        try {
          const branchInfo = JSON.parse(currentBranchCookie);
          branchId = branchInfo.branchId;
        } catch (error) {
          console.error("Error parsing current branch cookie:", error);
        }
      }
    }

    // Fallback to user's current branch
    if (!branchId && user) {
      try {
        const userWithBranch = await db.user.findUnique({
          where: { email: user.email },
          select: { currentBranchId: true },
        });

        if (userWithBranch?.currentBranchId) {
          branchId = userWithBranch.currentBranchId;
        }
      } catch (error) {
        console.error("Error getting user's current branch:", error);
      }
    }

    // Final fallback to first branch of organization
    if (!branchId) {
      try {
        const firstBranch = await db.branch.findFirst({
          where: { organizationId },
          orderBy: [{ isHeadOffice: "desc" }, { createdAt: "asc" }],
        });

        if (firstBranch) {
          branchId = firstBranch.id;
        }
      } catch (error) {
        console.error("Error getting first branch of organization:", error);
      }
    }
  }

  if (!branchId) {
    throw new Error("Branch ID is required and could not be determined automatically");
  }

  return { user, organizationId, branchId };
}

/**
 * Search patients with pagination and filtering
 */
export async function searchPatients(filters: PatientSearchFilters & { page?: number; limit?: number } = {}) {
  try {
    const { organizationId } = await getOrganizationContext();
    const page = filters.page || 1;
    const limit = filters.limit || 50;
    const skip = (page - 1) * limit;

    // Build where clause
    let where: any = {
      organizationId,
    };

    // Handle status filtering
    if (filters.status) {
      if (filters.status === "all") {
        // Show all patients including deleted (for admin purposes)
        // No additional status filter
      } else if (filters.status === "active") {
        where.status = "active";
      } else if (filters.status === "deleted") {
        where.status = "deleted";
      } else {
        // For any other specific status
        where.status = filters.status;
      }
    } else {
      // Default: only show active patients (exclude deleted)
      where.status = "active";
    }

    if (filters.search) {
      where.OR = [
        { firstName: { contains: filters.search, mode: "insensitive" } },
        { lastName: { contains: filters.search, mode: "insensitive" } },
        { email: { contains: filters.search, mode: "insensitive" } },
        { phone: { contains: filters.search, mode: "insensitive" } },
        { communicationMobile: { contains: filters.search, mode: "insensitive" } },
        { alternatePhone: { contains: filters.search, mode: "insensitive" } },
        { abhaProfile: { abhaNumber: { contains: filters.search, mode: "insensitive" } } },
        { abhaProfile: { abhaAddress: { contains: filters.search, mode: "insensitive" } } },
        { abhaProfile: { healthIdNumber: { contains: filters.search, mode: "insensitive" } } },
      ];
    }

    // Get patients with pagination
    const [patients, totalCount] = await Promise.all([
      db.patient.findMany({
        where,
        include: {
          abhaProfile: true,
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      db.patient.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    // Transform dates to strings for client compatibility
    const transformedPatients = patients.map(patient => ({
      ...patient,
      dateOfBirth: patient.dateOfBirth.toISOString().split('T')[0],
      createdAt: patient.createdAt.toISOString(),
      updatedAt: patient.updatedAt.toISOString(),
      insuranceExpiryDate: patient.insuranceExpiryDate?.toISOString().split('T')[0] || null,
      registrationDate: patient.createdAt.toISOString(),
    }));

    return {
      success: true,
      data: transformedPatients,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    };
  } catch (error) {
    console.error("Error searching patients:", error);
    return {
      success: false,
      error: "Failed to search patients",
      data: [],
      pagination: {
        page: 1,
        limit: 50,
        totalCount: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPrevPage: false,
      },
    };
  }
}

/**
 * Get all patients with optional filtering
 */
export async function getPatients(filters: PatientSearchFilters = {}) {
  try {
    const { organizationId } = await getOrganizationContext();

    // Build where clause
    let where: any = {
      organizationId,
    };

    // Handle status filtering - exclude deleted by default
    if (filters.status) {
      if (filters.status === "all") {
        // Show all patients including deleted (for admin purposes)
        // No additional status filter
      } else if (filters.status === "active") {
        where.status = "active";
      } else if (filters.status === "deleted") {
        where.status = "deleted";
      } else {
        // For any other specific status
        where.status = filters.status;
      }
    } else {
      // Default: only show active patients (exclude deleted)
      where.status = "active";
    }

    if (filters.search) {
      where.OR = [
        { firstName: { contains: filters.search, mode: "insensitive" } },
        { lastName: { contains: filters.search, mode: "insensitive" } },
        { phone: { contains: filters.search, mode: "insensitive" } },
        { communicationMobile: { contains: filters.search, mode: "insensitive" } },
        { email: { contains: filters.search, mode: "insensitive" } },
        {
          abhaProfile: {
            abhaAddress: { contains: filters.search, mode: "insensitive" }
          }
        },
        {
          abhaProfile: {
            abhaNumber: { contains: filters.search, mode: "insensitive" }
          }
        },
        {
          abhaProfile: {
            healthIdNumber: { contains: filters.search, mode: "insensitive" }
          }
        },
      ];
    }

    // Get patients with pagination
    const page = filters.page || 1;
    const limit = Math.min(filters.limit || 50, 100);
    const skip = (page - 1) * limit;

    const [patients, totalCount] = await Promise.all([
      db.patient.findMany({
        where,
        select: {
          id: true,
          firstName: true,
          lastName: true,
          dateOfBirth: true,
          gender: true,
          bloodGroup: true,
          maritalStatus: true,
          occupation: true,
          email: true,
          phone: true,
          alternatePhone: true,
          address: true,
          city: true,
          state: true,
          pincode: true,
          country: true,
          allergies: true,
          chronicDiseases: true,
          currentMedications: true,
          familyMedicalHistory: true,
          emergencyContactName: true,
          emergencyContactPhone: true,
          emergencyContactRelation: true,
          insuranceProvider: true,
          insurancePolicyNumber: true,
          insuranceExpiryDate: true,
          status: true,
          registrationDate: true,
          createdAt: true,
          updatedAt: true,
          primaryBranch: {
            select: {
              id: true,
              name: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          abhaProfile: {
            select: {
              id: true,
              abhaNumber: true,
              abhaAddress: true,
              healthIdNumber: true,
              abhaStatus: true,
              kycVerified: true,
            },
          },
          linkTokenRequests: {
            select: {
              id: true,
              status: true,
              createdAt: true,
              updatedAt: true,
            },
            orderBy: {
              createdAt: "desc",
            },
            take: 1,
          },
          abhaLinkTokens: {
            select: {
              id: true,
              status: true,
              linkToken: true,
              linkTokenExpiry: true,
              createdAt: true,
              updatedAt: true,
            },
            orderBy: {
              updatedAt: "desc",
            },
            take: 1,
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      db.patient.count({ where })
    ]);

    return {
      patients,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNextPage: page < Math.ceil(totalCount / limit),
        hasPrevPage: page > 1,
      }
    };
  } catch (error) {
    console.error("Error fetching patients:", error);
    throw new Error("Failed to fetch patients");
  }
}

/**
 * Get a single patient by ID
 */
export async function getPatient(patientId: string) {
  try {
    const { organizationId } = await getOrganizationContext();

    const patient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
      include: {
        primaryBranch: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        documents: true,
        abhaProfile: true,
      },
    });

    if (!patient) {
      throw new Error("Patient not found");
    }

    return patient;
  } catch (error) {
    console.error("Error fetching patient:", error);
    throw new Error("Failed to fetch patient");
  }
}

/**
 * Create a new patient
 */
export async function createPatient(formData: PatientFormData) {
  try {
    const { organizationId, branchId } = await getBranchContext(formData.primaryBranchId);

    // Validate required fields
    const requiredFields = ["firstName", "lastName", "dateOfBirth", "gender", "phone"];
    for (const field of requiredFields) {
      if (!formData[field as keyof PatientFormData]) {
        throw new Error(`${field} is required`);
      }
    }

    // Verify the branch exists and belongs to the organization
    const branch = await db.branch.findFirst({
      where: {
        id: branchId,
        organizationId,
      },
    });

    if (!branch) {
      throw new Error("Branch not found or does not belong to the organization");
    }

    // Check if ABHA details are provided
    const hasAbhaDetails = formData.abhaNumber || formData.abhaAddress || formData.healthIdNumber;

    // Check for duplicate ABHA profile within the same organization
    if (hasAbhaDetails && formData.abhaNumber && formData.abhaAddress) {
      const existingAbhaProfile = await db.abhaProfile.findFirst({
        where: {
          abhaNumber: formData.abhaNumber,
          abhaAddress: formData.abhaAddress,
          organizationId,
        },
        include: {
          patient: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      if (existingAbhaProfile) {
        throw new Error(`Patient with ABHA number ${formData.abhaNumber} and ABHA address ${formData.abhaAddress} already exists in this organization.`);
      }
    }

    // Check if email is provided
    const hasEmail = !!formData.email;

    // Use a transaction to ensure all operations succeed or fail together
    const result = await db.$transaction(async (tx) => {
      // Handle user creation/linking for the patient
      let userToLink = null;

      if (hasEmail) {
        // First, check if a user with this email already exists
        const existingUser = await tx.user.findUnique({
          where: { email: formData.email },
          include: {
            organizations: {
              where: { organizationId },
            },
          },
        });

        if (existingUser) {
          // Check if user is already linked to this organization
          if (existingUser.organizations.length === 0) {
            // User exists but not linked to this organization, link them
            await tx.userOrganization.create({
              data: {
                userId: existingUser.id,
                organizationId,
                roles: ["patient"],
                isDefault: false,
              },
            });
          }
          userToLink = existingUser;
        } else {
          // Create new user
          userToLink = await tx.user.create({
            data: {
              name: `${formData.firstName} ${formData.lastName}`,
              email: formData.email,
              role: "patient",
              organizations: {
                create: {
                  organizationId,
                  roles: ["patient"],
                  isDefault: true,
                },
              },
            },
          });
        }
      }

      // Create the patient
      const patient = await tx.patient.create({
        data: {
          // Include userId if a user was created or linked
          ...(userToLink && { userId: userToLink.id }),
          firstName: formData.firstName,
          lastName: formData.lastName,
          dateOfBirth: new Date(formData.dateOfBirth),
          gender: formData.gender,
          bloodGroup: formData.bloodGroup,
          maritalStatus: formData.maritalStatus,
          occupation: formData.occupation,
          email: formData.email,
          phone: formData.phone,
          communicationMobile: formData.communicationMobile,
          alternatePhone: formData.alternatePhone,
          address: formData.address,
          city: formData.city,
          state: formData.state,
          pincode: formData.pincode,
          country: formData.country || "India",
          allergies: formData.allergies,
          chronicDiseases: formData.chronicDiseases,
          currentMedications: formData.currentMedications,
          familyMedicalHistory: formData.familyMedicalHistory,
          emergencyContactName: formData.emergencyContactName,
          emergencyContactPhone: formData.emergencyContactPhone,
          emergencyContactRelation: formData.emergencyContactRelation,
          insuranceProvider: formData.insuranceProvider,
          insurancePolicyNumber: formData.insurancePolicyNumber,
          insuranceExpiryDate: formData.insuranceExpiryDate ? new Date(formData.insuranceExpiryDate) : null,
          status: formData.status || "active",
          primaryBranchId: branchId,
          organizationId,
          // Create ABHA profile if ABHA details are provided
          ...(hasAbhaDetails && {
            abhaProfile: {
              create: {
                abhaNumber: formData.abhaNumber,
                abhaAddress: formData.abhaAddress,
                healthIdNumber: formData.healthIdNumber,
                abhaStatus: formData.abhaStatus || "VERIFIED",
                organizationId,
                kycVerified: (() => {
                  if (formData.fromAbhaVerification === true || formData.fromAbhaVerification === "true") {
                    return true;
                  }
                  if (formData.kycVerified === true) {
                    return true;
                  }
                  return false;
                })(),
              },
            },
          }),
        },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          dateOfBirth: true,
          gender: true,
          bloodGroup: true,
          maritalStatus: true,
          occupation: true,
          email: true,
          phone: true,
          alternatePhone: true,
          address: true,
          city: true,
          state: true,
          pincode: true,
          country: true,
          allergies: true,
          chronicDiseases: true,
          currentMedications: true,
          familyMedicalHistory: true,
          emergencyContactName: true,
          emergencyContactPhone: true,
          emergencyContactRelation: true,
          insuranceProvider: true,
          insurancePolicyNumber: true,
          insuranceExpiryDate: true,
          status: true,
          registrationDate: true,
          createdAt: true,
          updatedAt: true,
          primaryBranchId: true,
          organizationId: true,
          userId: true,
        },
      });

      return { patient };
    });

    // Generate link token if ABHA details are provided
    if (hasAbhaDetails && formData.abhaNumber && formData.abhaAddress) {
      try {
        // Import the link token generation service
        const { generateLinkToken } = await import("@/services/abdm/care-context/link-token");

        // Generate link token
        await generateLinkToken(result.patient.id, branchId);
      } catch (linkTokenError) {
        // Log error but don't fail the patient creation
        console.error("Error generating link token for patient:", linkTokenError);
      }
    }

    // Revalidate the patients page
    revalidatePath("/patients");

    return result.patient;
  } catch (error) {
    console.error("Error creating patient:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to create patient");
  }
}

/**
 * Search patient by phone number
 */
export async function searchPatientByPhone(phone: string) {
  try {
    const { organizationId } = await getOrganizationContext();

    const patient = await db.patient.findFirst({
      where: {
        organizationId,
        OR: [
          { phone: phone },
          { communicationMobile: phone },
          { alternatePhone: phone },
        ],
      },
      include: {
        primaryBranch: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        abhaProfile: true,
      },
    });

    return { patient };
  } catch (error) {
    console.error("Error searching patient by phone:", error);
    throw new Error("Failed to search patient by phone");
  }
}

/**
 * Update an existing patient
 */
export async function updatePatient(patientId: string, formData: Partial<PatientFormData>) {
  try {
    const { organizationId } = await getOrganizationContext();

    // Remove fields that shouldn't be updated
    const protectedFields = ["id", "createdAt", "organizationId"];
    protectedFields.forEach(field => delete (formData as any)[field]);

    // Check if patient exists and belongs to organization
    const existingPatient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
      include: {
        user: true,
      },
    });

    if (!existingPatient) {
      throw new Error("Patient not found or does not belong to the organization");
    }

    // Handle date fields
    const updateData: any = { ...formData };
    Object.keys(updateData).forEach(key => {
      if (typeof updateData[key] === "string" && updateData[key].match(/^\d{4}-\d{2}-\d{2}/)) {
        updateData[key] = new Date(updateData[key]);
      }
    });

    // Update the patient
    const updatedPatient = await db.patient.update({
      where: { id: patientId },
      data: updateData,
      include: {
        primaryBranch: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        documents: true,
        abhaProfile: true,
      },
    });

    // Revalidate the patients page and individual patient page
    revalidatePath("/patients");
    revalidatePath(`/patients/${patientId}`);

    return updatedPatient;
  } catch (error) {
    console.error("Error updating patient:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to update patient");
  }
}

/**
 * Delete a patient (soft delete by default)
 */
export async function deletePatient(patientId: string, hardDelete: boolean = false) {
  try {
    const { organizationId } = await getOrganizationContext();

    // Check if patient exists and belongs to organization
    const existingPatient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
      include: {
        user: true,
      },
    });

    if (!existingPatient) {
      throw new Error("Patient not found or does not belong to the organization");
    }

    if (hardDelete) {
      // First, delete any HealthRecordFetch records for this patient
      await db.healthRecordFetch.deleteMany({
        where: {
          patientId: patientId,
        },
      });

      // Hard delete the patient (this will cascade to patient documents)
      await db.patient.delete({
        where: {
          id: patientId,
        },
      });
    } else {
      // Soft delete - update status to "deleted"
      await db.patient.update({
        where: { id: patientId },
        data: { status: "deleted" },
      });
    }

    // Revalidate the patients page
    revalidatePath("/patients");

    return { success: true, hardDelete };
  } catch (error) {
    console.error("Error deleting patient:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to delete patient");
  }
}

/**
 * Search patients by phone and check ABHA
 */
export async function searchPhoneAndCheckABHA(phone: string) {
  try {
    const { organizationId } = await getOrganizationContext();

    // Search for existing patient in DB
    const patient = await db.patient.findFirst({
      where: {
        organizationId,
        OR: [
          { phone: phone },
          { communicationMobile: phone },
          { alternatePhone: phone },
        ],
      },
      include: {
        abhaProfile: true,
        primaryBranch: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Search for ABHA (this would typically call external ABDM API)
    let abhaResult = null;
    let abhaError = null;

    try {
      // Import and use the ABHA search service
      const { searchAbhaByMobile } = await import("@/services/patient-service");
      abhaResult = await searchAbhaByMobile(phone);
    } catch (error) {
      console.log("ABHA search failed or no ABHA found:", error);
      abhaError = error instanceof Error ? error.message : "ABHA search failed";
    }

    return {
      patient: patient || null,
      abhaData: abhaResult || null,
      abhaError,
      hasPatient: !!patient,
      hasAbha: !!abhaResult,
      patientHasAbhaProfile: !!patient?.abhaProfile,
    };
  } catch (error) {
    console.error("Error searching phone and ABHA:", error);
    throw new Error("Failed to search phone and ABHA");
  }
}

/**
 * Get patient by ABHA address
 */
export async function getPatientByAbhaAddress(abhaAddress: string) {
  try {
    const { organizationId } = await getOrganizationContext();

    const patient = await db.patient.findFirst({
      where: {
        organizationId,
        abhaProfile: {
          abhaAddress,
        },
      },
      include: {
        abhaProfile: {
          select: {
            abhaNumber: true,
            abhaAddress: true,
          },
        },
      },
    });

    if (!patient) {
      return null;
    }

    return patient;
  } catch (error) {
    console.error("Error fetching patient by ABHA address:", error);
    throw new Error("Failed to fetch patient by ABHA address");
  }
}

/**
 * Get patient appointments
 */
export async function getPatientAppointments(patientId: string) {
  try {
    const { organizationId } = await getOrganizationContext();

    // Verify patient exists and belongs to organization
    const patient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
    });

    if (!patient) {
      throw new Error("Patient not found");
    }

    const appointments = await db.appointment.findMany({
      where: {
        patientId: patientId,
      },
      include: {
        doctor: {
          include: {
            user: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        appointmentDate: "desc",
      },
    });

    return appointments;
  } catch (error) {
    console.error("Error fetching patient appointments:", error);
    throw new Error("Failed to fetch patient appointments");
  }
}

/**
 * Get patient consultations
 */
export async function getPatientConsultations(patientId: string) {
  try {
    const { organizationId } = await getOrganizationContext();

    // Verify patient exists and belongs to organization
    const patient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
    });

    if (!patient) {
      throw new Error("Patient not found");
    }

    const consultations = await db.consultation.findMany({
      where: {
        patientId: patientId,
      },
      include: {
        doctor: {
          include: {
            user: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
        vitals: {
          orderBy: {
            recordedAt: "desc",
          },
          take: 1,
        },
        clinicalNotes: {
          orderBy: {
            createdAt: "desc",
          },
        },
        prescriptions: {
          include: {
            items: true,
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
      orderBy: {
        consultationDate: "desc",
      },
    });

    return consultations;
  } catch (error) {
    console.error("Error fetching patient consultations:", error);
    throw new Error("Failed to fetch patient consultations");
  }
}

/**
 * Get patient care contexts
 */
export async function getPatientCareContexts(patientId: string, branchId?: string) {
  try {
    const { organizationId } = await getOrganizationContext();

    // Verify patient exists and belongs to organization
    const patient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
      include: {
        abhaProfile: true,
        user: true,
      },
    });

    if (!patient) {
      throw new Error("Patient not found");
    }

    let where: any = {
      patientId: patientId,
    };

    if (branchId) {
      where.branchId = branchId;
    }

    const careContexts = await db.careContext.findMany({
      where,
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            abhaProfile: {
              select: {
                abhaNumber: true,
                abhaAddress: true,
              },
            },
          },
        },

      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return { patient, careContexts };
  } catch (error) {
    console.error("Error fetching patient care contexts:", error);
    throw new Error("Failed to fetch patient care contexts");
  }
}

/**
 * Redirect to patient registration with success message
 */
export async function redirectToPatientSuccess(patientId: string) {
  redirect(`/patients/${patientId}?success=true`);
}

/**
 * Redirect to patients list
 */
export async function redirectToPatients() {
  redirect("/patients");
}
