export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { parseAbdmError } from "@/lib/abdm-error-utils";

/**
 * Webhook handler for ABDM generate token error notifications
 * POST /api/webhook/api/v3/hip/token/generate-token-notify
 *
 * This webhook receives error notifications when token generation fails
 * and stores them in the GenerateTokenNotify model
 */
export async function POST(req: NextRequest) {
  try {
    // Get the request ID from headers
    const requestId = req.headers.get("request-id") || "unknown";

    // Parse the webhook payload
    const payload = await req.json();

    // Log the received webhook payload
    console.log(
      `Received ABDM generate token notification webhook (requestId: ${requestId}):`,
      JSON.stringify(payload, null, 2),
    );

    // Check if payload contains error information
    if (!payload) {
      console.error("Missing payload in webhook request");
      return NextResponse.json({ error: "Missing payload" }, { status: 400 });
    }

    // Store the notification in the database
    try {
      // Parse the error if present
      let parsedError = null;
      if (payload.error) {
        parsedError = parseAbdmError(payload.error);
        console.log("Parsed ABDM error:", parsedError);
      }

      // Log available models for debugging
      console.log("Available Prisma models:", Object.keys(db));

      // Use parameterized query to prevent SQL injection
      const uniqueId = `cuid-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
      const requestIdValue = payload.response?.requestId || requestId;
      const errorValue = payload.error ? JSON.stringify(payload.error) : null;
      const responseValue = payload.response ? JSON.stringify(payload.response) : null;

      await db.$executeRaw`
        INSERT INTO "GenerateTokenNotify" (
          id,
          "requestId",
          error,
          response,
          "createdAt",
          "updatedAt"
        ) VALUES (
          ${uniqueId},
          ${requestIdValue},
          ${errorValue}::jsonb,
          ${responseValue}::jsonb,
          NOW(),
          NOW()
        )
      `;

      console.log(
        `Successfully stored generate token notification for requestId: ${requestId}`,
      );

      // Update the request context status to failed if there's an error
      if (payload.error) {
        try {
          const actualRequestId = payload.response?.requestId || requestId;
          await db.linkTokenRequest.update({
            where: {
              requestId: actualRequestId,
            },
            data: {
              status: "failed",
              updatedAt: new Date(),
            },
          });
          console.log(
            `Updated request context status to failed for requestId: ${actualRequestId}`,
          );
        } catch (updateError) {
          console.error("Error updating request context status:", updateError);
        }
      }

      // Return success response
      return NextResponse.json({
        acknowledgement: {
          status: "SUCCESS",
        },
        response: {
          requestId,
        },
      });
    } catch (dbError) {
      console.error("Error storing generate token notification:", dbError);
      return NextResponse.json(
        { error: "Failed to store generate token notification" },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error(
      "Error processing ABDM generate token notification webhook:",
      error,
    );

    const reqId = req.headers.get("request-id") || "unknown";

    return NextResponse.json(
      {
        acknowledgement: {
          status: "error",
          error: {
            code: "INTERNAL_SERVER_ERROR",
            message: error instanceof Error ? error.message : "Unknown error",
          },
        },
        response: {
          requestId: reqId,
        },
      },
      { status: 500 },
    );
  }
}
