export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";

/**
 * Webhook handler for ABDM link token generation
 * POST /api/webhook/v3/hip/token/on-generate-token
 */
export async function POST(req: NextRequest) {
  try {
    // Parse the webhook payload
    const payload = await req.json();

    // Log the received webhook payload
    console.log(
      "Received ABDM link token webhook:",
      JSON.stringify(payload, null, 2),
    );

    // Extract the link token, ABHA address, and request ID
    const { abhaAddress, linkToken } = payload;
    const requestId = payload.response?.requestId;

    if (!abhaAddress) {
      console.error("Missing ABHA address in webhook payload");
      return NextResponse.json(
        { error: "Missing ABHA address" },
        { status: 400 },
      );
    }

    if (!linkToken) {
      console.error("Missing link token in webhook payload");
      return NextResponse.json(
        { error: "Missing link token" },
        { status: 400 },
      );
    }

    if (!requestId) {
      console.error("Missing requestId in webhook payload");
      return NextResponse.json(
        { error: "Missing requestId in webhook payload" },
        { status: 400 },
      );
    }

    // First, try to find the request context using the requestId
    const requestContext = await db.linkTokenRequest.findUnique({
      where: {
        requestId,
      },
      include: {
        patient: true,
        branch: true,
        organization: true,
      },
    });

    if (!requestContext) {
      console.error(`No request context found for requestId: ${requestId}`);
      return NextResponse.json(
        { error: "No request context found for this requestId" },
        { status: 404 },
      );
    }

    console.log(`Found request context for requestId: ${requestId}`, {
      patientId: requestContext.patientId,
      branchId: requestContext.branchId,
      organizationId: requestContext.organizationId,
      hipId: requestContext.hipId,
    });

    // Store the link token in the AbhaLinkToken model using the request context
    try {
      const patient = requestContext.patient;
      const branch = requestContext.branch;
      const organization = requestContext.organization;

      console.log(`Storing link token for:`, {
        patientId: patient.id,
        branchId: branch.id,
        organizationId: organization.id,
        hipId: requestContext.hipId,
        requestId,
      });

      // Look for existing pending token for this specific patient-branch-organization combination
      let existingToken = await db.abhaLinkToken.findFirst({
        where: {
          patientId: patient.id,
          branchId: branch.id,
          organizationId: organization.id,
          status: "pending",
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      if (existingToken) {
        console.log(
          `Found pending token for patient ${patient.id}, branch ${branch.id}, organization ${organization.id}: ${existingToken.id}`,
        );
      } else {
        console.log(
          `No pending token found for patient ${patient.id}, branch ${branch.id}, organization ${organization.id}`,
        );
      }

      if (existingToken) {
        // Update existing token
        await db.abhaLinkToken.update({
          where: {
            id: existingToken.id,
          },
          data: {
            linkToken,
            linkTokenExpiry: new Date(
              new Date().setMonth(new Date().getMonth() + 6),
            ), // 6 months expiry
            status: "active",
            requestId,
            updatedAt: new Date(),
          },
        });
        console.log(
          `Updated existing token ${existingToken.id} with new link token`,
        );
      } else {
        // Create new token using the request context
        const newToken = await db.abhaLinkToken.create({
          data: {
            patientId: patient.id,
            branchId: branch.id,
            organizationId: organization.id,
            hipId: requestContext.hipId,
            linkToken,
            linkTokenExpiry: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours expiry
            status: "active",
            requestId,
          },
        });
        console.log(
          `Created new token ${newToken.id} for branch ${branch.id}, organization ${organization.id}`,
        );
      }

      // Update the request context status to completed
      await db.linkTokenRequest.update({
        where: {
          requestId,
        },
        data: {
          status: "completed",
          updatedAt: new Date(),
        },
      });
      console.log(
        `Updated request context status to completed for requestId: ${requestId}`,
      );

      // Also store the successful token generation in GenerateTokenNotify
      try {
        const uniqueId = `cuid-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

        // Create a response object with the necessary information
        const responseObj = {
          requestId,
          abhaAddress,
          linkToken,
          branchId: branch.id,
          organizationId: organization.id,
          patientId: patient.id,
        };

        // Store in GenerateTokenNotify table using parameterized query
        const responseValue = JSON.stringify(responseObj);

        await db.$executeRaw`
          INSERT INTO "GenerateTokenNotify" (
            id,
            "requestId",
            error,
            response,
            "createdAt",
            "updatedAt"
          ) VALUES (
            ${uniqueId},
            ${requestId},
            null,
            ${responseValue}::jsonb,
            NOW(),
            NOW()
          )
        `;

        console.log(
          `Successfully stored successful token generation in GenerateTokenNotify for requestId: ${requestId}`,
        );
      } catch (notifyError) {
        // Just log the error but don't fail the main operation
        console.error(
          "Error storing successful token generation in GenerateTokenNotify:",
          notifyError,
        );
      }

      // Return a success response
      return NextResponse.json({
        status: "success",
        message: "Link token received and stored successfully",
      });
    } catch (dbError) {
      console.error("Error storing link token from webhook:", dbError);
      return NextResponse.json(
        { error: "Failed to store link token" },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Error processing ABDM link token webhook:", error);
    return NextResponse.json(
      { error: "Failed to process webhook" },
      { status: 500 },
    );
  }
}
