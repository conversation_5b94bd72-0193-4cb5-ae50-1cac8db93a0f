import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";

/**
 * Utility endpoint to check the contents of the GenerateTokenNotify table
 * GET /api/webhook/api/v3/hip/token/generate-token-notify/check
 */
export async function GET(_req: NextRequest) {
  try {
    // Query the database with parameterized query for security
    const rawResults = await db.$queryRaw`
      SELECT
        id,
        "requestId",
        error::text as error,
        response::text as response,
        "createdAt",
        "updatedAt"
      FROM "GenerateTokenNotify"
      ORDER BY "createdAt" DESC
      LIMIT 10
    `;

    // Parse JSON strings back to objects
    const results = (rawResults as any[]).map((record) => ({
      ...record,
      error: record.error ? JSON.parse(record.error) : null,
      response: record.response ? JSON.parse(record.response) : null,
    }));

    return NextResponse.json({
      message: "Successfully retrieved GenerateTokenNotify records",
      count: results.length,
      records: results,
    });
  } catch (error) {
    console.error("Error retrieving GenerateTokenNotify records:", error);

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
