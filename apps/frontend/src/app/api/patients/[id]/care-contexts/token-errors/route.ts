export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/session";

// GET /api/patients/[id]/care-contexts/token-errors - Get token generation errors for a patient
export async function GET(
  _req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const patientId = params.id;

    // Get the patient with ABHA profile
    const patient = await db.patient.findUnique({
      where: { id: patientId },
      include: { abhaProfile: true },
    });

    if (!patient) {
      return NextResponse.json({ error: "Patient not found" }, { status: 404 });
    }

    if (!patient.abhaProfile?.abhaAddress) {
      return NextResponse.json(
        {
          error: "Patient does not have an ABHA address",
          errors: [],
        },
        { status: 400 },
      );
    }

    // Query the GenerateTokenNotify table for notifications related to this patient
    // Using parameterized queries to prevent SQL injection
    const abhaAddress = patient.abhaProfile.abhaAddress;
    const abhaNumber = patient.abhaProfile.abhaNumber || "";

    const tokenErrors = await db.$queryRaw`
      SELECT
        id,
        "requestId",
        error::text as error,
        response::text as response,
        "createdAt",
        "updatedAt"
      FROM "GenerateTokenNotify"
      WHERE error::text LIKE ${`%${abhaAddress}%`}
      OR error::text LIKE ${`%${abhaNumber}%`}
      OR response::text LIKE ${`%${abhaAddress}%`}
      ORDER BY "createdAt" DESC
      LIMIT 10
    `;

    // Parse JSON strings back to objects
    const formattedErrors = (tokenErrors as any[]).map((record) => ({
      ...record,
      error: record.error ? JSON.parse(record.error) : null,
      response: record.response ? JSON.parse(record.response) : null,
    }));

    return NextResponse.json({
      errors: formattedErrors,
    });
  } catch (error) {
    console.error("Error fetching token errors:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Something went wrong",
      },
      { status: 500 },
    );
  }
}
