export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import {
  validateAuth,
  validateDocType,
  buildWhereClause,
  getModelDelegate,
  getModelInclude,
  getRequiredFields,
  validateRequiredFields,
  parsePaginationParams,
  parseSearchParams,
  ErrorResponses,
  logApiActivity,
  ORGANIZATION_FILTERED_MODELS
} from "@/lib/generic-api-utils";

/**
 * GET /api/generic/{doctype} - Get multiple records by DOCTYPE with filtering and pagination
 * 
 * @param doctype - The document type (maps to Prisma model)
 * 
 * Query Parameters:
 * - page: Page number (default: 1)
 * - limit: Records per page (default: 50, max: 100)
 * - search: Search term for text fields
 * - filter_{fieldName}: Filter by specific field value
 * - sort_{fieldName}: Sort by field (asc|desc)
 * - include: Comma-separated list of relations to include
 * - fields: Comma-separated list of fields to select
 * 
 * Example: GET /api/generic/patient?page=1&limit=20&search=john&filter_status=active&sort_createdAt=desc&include=abhaProfile
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { doctype: string } }
) {
  try {
    // Validate authentication and get user context
    const { user, organizationId, error: authError } = await validateAuth();
    if (authError) return authError;

    // Validate DOCTYPE
    const { isValid, modelName, error: doctypeError } = validateDocType(params.doctype);
    if (!isValid || !modelName) return doctypeError!;

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const { page, limit, offset } = parsePaginationParams(searchParams);
    const { search, filters, sort } = parseSearchParams(searchParams);
    const includeParam = searchParams.get("include");
    const fieldsParam = searchParams.get("fields");

    // Build base where clause
    let whereClause = buildWhereClause(modelName, organizationId, filters);

    // Add search functionality for common text fields
    if (search) {
      const searchFields = getSearchFields(modelName);
      if (searchFields.length > 0) {
        whereClause.OR = searchFields.map(field => ({
          [field]: { contains: search, mode: "insensitive" }
        }));
      }
    }

    // Build query options
    const queryOptions: any = {
      where: whereClause,
      skip: offset,
      take: limit,
      orderBy: sort
    };

    // Handle field selection
    if (fieldsParam) {
      const fields = fieldsParam.split(",").map(f => f.trim());
      queryOptions.select = {};
      fields.forEach(field => {
        queryOptions.select[field] = true;
      });
    }

    // Handle includes
    if (includeParam) {
      const includes = includeParam.split(",").map(i => i.trim());
      queryOptions.include = {};
      includes.forEach(include => {
        queryOptions.include[include] = true;
      });
    } else {
      // Use default includes for the model
      queryOptions.include = getModelInclude(modelName);
    }

    // Get the model delegate
    const model = getModelDelegate(modelName);
    if (!model) {
      return ErrorResponses.badRequest(`Model ${modelName} not found`);
    }

    // Execute queries in parallel
    const [records, totalCount] = await Promise.all([
      model.findMany(queryOptions),
      model.count({ where: whereClause })
    ]);
    if (!limit || !page) {
       throw new Error("Invalid pagination parameters");
    }

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // Log the activity
    await logApiActivity(
      "LIST",
      modelName,
      "multiple",
      user.id,
      organizationId,
      { 
        doctype: params.doctype,
        count: records.length,
        filters: Object.keys(filters),
        search: !!search
      }
    );

    return NextResponse.json({
      success: true,
      data: records,
      doctype: params.doctype,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage,
        hasPrevPage
      },
      filters: filters,
      search: search || null
    });

  } catch (error) {
    console.error(`Error fetching ${params.doctype} records:`, error);
    return ErrorResponses.serverError(
      `Failed to fetch ${params.doctype} records`
    );
  }
}

/**
 * POST /api/generic/{doctype} - Create a new record by DOCTYPE
 * 
 * @param doctype - The document type (maps to Prisma model)
 * 
 * Request Body: JSON object with record data
 * 
 * Example: POST /api/generic/patient
 * Body: { "firstName": "John", "lastName": "Doe", "dateOfBirth": "1990-01-01", ... }
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { doctype: string } }
) {
  try {
    // Validate authentication and get user context
    const { user, organizationId, error: authError } = await validateAuth();
    if (authError) return authError;

    // Validate DOCTYPE
    const { isValid, modelName, error: doctypeError } = validateDocType(params.doctype);
    if (!isValid || !modelName) return doctypeError!;

    // Parse request body
    const createData = await req.json();

    if (!createData || Object.keys(createData).length === 0) {
      return ErrorResponses.badRequest("No data provided");
    }

    // Validate required fields
    const requiredFields = getRequiredFields(modelName);
    const { isValid: fieldsValid, missingFields } = validateRequiredFields(createData, requiredFields);
    
    if (!fieldsValid && missingFields) {
      return ErrorResponses.validationError({
        message: "Missing required fields",
        missingFields
      });
    }

    // Add organizationId if model requires it
    if (ORGANIZATION_FILTERED_MODELS.includes(modelName)) {
      createData.organizationId = organizationId;
    }

    // Handle date fields
    Object.keys(createData).forEach(key => {
      if (typeof createData[key] === "string" && createData[key].match(/^\d{4}-\d{2}-\d{2}/)) {
        createData[key] = new Date(createData[key]);
      }
    });

    // Get the model delegate
    const model = getModelDelegate(modelName);
    if (!model) {
      return ErrorResponses.badRequest(`Model ${modelName} not found`);
    }

    // Create the record
    const newRecord = await model.create({
      data: createData,
      include: getModelInclude(modelName)
    });

    // Log the activity
    await logApiActivity(
      "CREATE",
      modelName,
      newRecord.id,
      user.id,
      organizationId,
      { doctype: params.doctype }
    );

    return NextResponse.json({
      success: true,
      data: newRecord,
      doctype: params.doctype,
      message: `${params.doctype} created successfully`
    }, { status: 201 });

  } catch (error) {
    console.error(`Error creating ${params.doctype} record:`, error);
    
    // Handle Prisma unique constraint errors
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return ErrorResponses.conflict(
        `${params.doctype} with this data already exists`
      );
    }

    return ErrorResponses.serverError(
      `Failed to create ${params.doctype} record`
    );
  }
}

// Helper function to get searchable fields for each model
function getSearchFields(modelName: string): string[] {
  const searchFieldsMap: Record<string, string[]> = {
    patient: ["firstName", "lastName", "phone", "email"],
    doctor: ["specialization"],
    consultation: [],
    appointment: [],
    vitals: [],
    prescription: [],
    clinicalNote: ["content", "chiefComplaints"],
    diagnosticReport: ["reportType", "conclusion"],
    immunization: ["vaccineCode"],
    procedure: ["code"],
    allergyIntolerance: ["code"],
    invoice: [],
    labTestRequest: ["testType", "testName"],
    careType: ["name", "description"],
    staff: ["name", "email"],
    branch: ["name"],
    department: ["name", "description"],
    organization: ["name"],
    user: ["name", "email"]
  };

  return searchFieldsMap[modelName] || [];
}
