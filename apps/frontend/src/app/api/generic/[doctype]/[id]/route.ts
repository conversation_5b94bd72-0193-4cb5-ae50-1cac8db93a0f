export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import {
  validateAuth,
  validateDocType,
  buildWhereClause,
  getModelDelegate,
  getModelInclude,
  ErrorResponses,
  logApiActivity
} from "@/lib/generic-api-utils";

/**
 * GET /api/generic/{doctype}/{id} - Get a single record by ID and DOCTYPE
 * 
 * @param doctype - The document type (maps to Prisma model)
 * @param id - The record ID
 * 
 * Query Parameters:
 * - include: Comma-separated list of relations to include
 * - fields: Comma-separated list of fields to select
 * 
 * Example: GET /api/generic/patient/123?include=abhaProfile,primaryBranch&fields=id,firstName,lastName
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { doctype: string; id: string } }
) {
  try {
    // Validate authentication and get user context
    const { user, organizationId, error: authError } = await validateAuth();
    if (authError) return authError;

    // Validate DOCTYPE
    const { isValid, modelName, error: doctypeError } = validateDocType(params.doctype);
    if (!isValid || !modelName) return doctypeError!;

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const includeParam = searchParams.get("include");
    const fieldsParam = searchParams.get("fields");

    // Build query options
    const queryOptions: any = {
      where: buildWhereClause(modelName, organizationId, { id: params.id })
    };

    // Handle field selection
    if (fieldsParam) {
      const fields = fieldsParam.split(",").map(f => f.trim());
      queryOptions.select = {};
      fields.forEach(field => {
        queryOptions.select[field] = true;
      });
    }

    // Handle includes
    if (includeParam) {
      const includes = includeParam.split(",").map(i => i.trim());
      queryOptions.include = {};
      includes.forEach(include => {
        queryOptions.include[include] = true;
      });
    } else {
      // Use default includes for the model
      queryOptions.include = getModelInclude(modelName);
    }

    // Get the model delegate and fetch the record
    const model = getModelDelegate(modelName);
    if (!model) {
      return ErrorResponses.badRequest(`Model ${modelName} not found`);
    }

    const record = await model.findFirst(queryOptions);

    if (!record) {
      return ErrorResponses.notFound(`${params.doctype} record`);
    }

    // Log the activity
    await logApiActivity(
      "READ",
      modelName,
      params.id,
      user.id,
      organizationId,
      { doctype: params.doctype }
    );

    return NextResponse.json({
      success: true,
      data: record,
      doctype: params.doctype,
      id: params.id
    });

  } catch (error) {
    console.error(`Error fetching ${params.doctype} record:`, error);
    return ErrorResponses.serverError(
      `Failed to fetch ${params.doctype} record`
    );
  }
}

/**
 * PATCH /api/generic/{doctype}/{id} - Update a record by ID and DOCTYPE
 * 
 * @param doctype - The document type (maps to Prisma model)
 * @param id - The record ID
 * 
 * Request Body: JSON object with fields to update
 * 
 * Example: PATCH /api/generic/patient/123
 * Body: { "firstName": "John", "lastName": "Doe" }
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: { doctype: string; id: string } }
) {
  try {
    // Validate authentication and get user context
    const { user, organizationId, error: authError } = await validateAuth();
    if (authError) return authError;

    // Validate DOCTYPE
    const { isValid, modelName, error: doctypeError } = validateDocType(params.doctype);
    if (!isValid || !modelName) return doctypeError!;

    // Parse request body
    const updateData = await req.json();

    if (!updateData || Object.keys(updateData).length === 0) {
      return ErrorResponses.badRequest("No update data provided");
    }

    // Remove fields that shouldn't be updated
    const protectedFields = ["id", "createdAt", "organizationId"];
    protectedFields.forEach(field => delete updateData[field]);

    // Build where clause to ensure record exists and user has access
    const whereClause = buildWhereClause(modelName, organizationId, { id: params.id });

    // Get the model delegate
    const model = getModelDelegate(modelName);
    if (!model) {
      return ErrorResponses.badRequest(`Model ${modelName} not found`);
    }

    // Check if record exists first
    const existingRecord = await model.findFirst({
      where: whereClause,
      select: { id: true }
    });

    if (!existingRecord) {
      return ErrorResponses.notFound(`${params.doctype} record`);
    }

    // Handle date fields
    Object.keys(updateData).forEach(key => {
      if (typeof updateData[key] === "string" && updateData[key].match(/^\d{4}-\d{2}-\d{2}/)) {
        updateData[key] = new Date(updateData[key]);
      }
    });

    // Update the record
    const updatedRecord = await model.update({
      where: { id: params.id },
      data: updateData,
      include: getModelInclude(modelName)
    });

    // Log the activity
    await logApiActivity(
      "UPDATE",
      modelName,
      params.id,
      user.id,
      organizationId,
      { doctype: params.doctype, updatedFields: Object.keys(updateData) }
    );

    return NextResponse.json({
      success: true,
      data: updatedRecord,
      doctype: params.doctype,
      id: params.id,
      message: `${params.doctype} updated successfully`
    });

  } catch (error) {
    console.error(`Error updating ${params.doctype} record:`, error);
    
    // Handle Prisma errors
    if (error instanceof Error && error.message.includes("Record to update not found")) {
      return ErrorResponses.notFound(`${params.doctype} record`);
    }

    return ErrorResponses.serverError(
      `Failed to update ${params.doctype} record`
    );
  }
}

/**
 * DELETE /api/generic/{doctype}/{id} - Delete a record by ID and DOCTYPE
 * 
 * @param doctype - The document type (maps to Prisma model)
 * @param id - The record ID
 * 
 * Query Parameters:
 * - soft: Set to "true" for soft delete (sets status to "deleted")
 * 
 * Example: DELETE /api/generic/patient/123?soft=true
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { doctype: string; id: string } }
) {
  try {
    // Validate authentication and get user context
    const { user, organizationId, error: authError } = await validateAuth();
    if (authError) return authError;

    // Validate DOCTYPE
    const { isValid, modelName, error: doctypeError } = validateDocType(params.doctype);
    if (!isValid || !modelName) return doctypeError!;

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const softDelete = searchParams.get("soft") === "true";

    // Build where clause to ensure record exists and user has access
    const whereClause = buildWhereClause(modelName, organizationId, { id: params.id });

    // Get the model delegate
    const model = getModelDelegate(modelName);
    if (!model) {
      return ErrorResponses.badRequest(`Model ${modelName} not found`);
    }

    // Check if record exists first
    const existingRecord = await model.findFirst({
      where: whereClause,
      select: { id: true }
    });

    if (!existingRecord) {
      return ErrorResponses.notFound(`${params.doctype} record`);
    }

    let action = "DELETE";

    if (softDelete) {
      // Soft delete - update status to "deleted"
      await model.update({
        where: { id: params.id },
        data: { status: "deleted" },
        select: { id: true, status: true }
      });
      action = "SOFT_DELETE";
    } else {
      // Hard delete
      await model.delete({
        where: { id: params.id }
      });
    }

    // Log the activity
    await logApiActivity(
      action,
      modelName,
      params.id,
      user.id,
      organizationId,
      { doctype: params.doctype, softDelete }
    );

    return NextResponse.json({
      success: true,
      doctype: params.doctype,
      id: params.id,
      message: `${params.doctype} ${softDelete ? 'soft ' : ''}deleted successfully`,
      softDelete
    });

  } catch (error) {
    console.error(`Error deleting ${params.doctype} record:`, error);
    
    // Handle Prisma errors
    if (error instanceof Error && error.message.includes("Record to delete does not exist")) {
      return ErrorResponses.notFound(`${params.doctype} record`);
    }

    return ErrorResponses.serverError(
      `Failed to delete ${params.doctype} record`
    );
  }
}
