export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { DOCTYPE_MODEL_MAP } from "@/lib/generic-api-utils";

/**
 * GET /api/generic/test - Test endpoint to validate generic API setup
 * 
 * This endpoint provides information about the generic API configuration
 * and can be used to test if the system is working correctly.
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const doctype = searchParams.get("doctype");

    // If specific doctype is requested, provide detailed info
    if (doctype) {
      const modelName = DOCTYPE_MODEL_MAP[doctype.toLowerCase()];
      
      if (!modelName) {
        return NextResponse.json({
          error: "Invalid DOCTYPE",
          doctype,
          supportedTypes: Object.keys(DOCTYPE_MODEL_MAP)
        }, { status: 400 });
      }

      return NextResponse.json({
        success: true,
        doctype,
        modelName,
        endpoints: {
          getRecord: `GET /api/generic/${doctype}/{id}`,
          getRecords: `GET /api/generic/${doctype}`,
          create: `POST /api/generic/${doctype}`,
          update: `PATCH /api/generic/${doctype}/{id}`,
          delete: `DELETE /api/generic/${doctype}/{id}`
        },
        examples: {
          getRecord: `/api/generic/${doctype}/123?include=related&fields=id,name`,
          getRecords: `/api/generic/${doctype}?page=1&limit=20&search=term&filter_status=active&sort_createdAt=desc`,
          create: {
            method: "POST",
            url: `/api/generic/${doctype}`,
            body: "JSON object with record data"
          },
          update: {
            method: "PATCH", 
            url: `/api/generic/${doctype}/123`,
            body: "JSON object with fields to update"
          },
          delete: {
            method: "DELETE",
            url: `/api/generic/${doctype}/123?soft=true`
          }
        }
      });
    }

    // Return general API information
    return NextResponse.json({
      success: true,
      message: "Generic API is working correctly",
      version: "1.0.0",
      endpoints: {
        getRecord: "GET /api/generic/{doctype}/{id}",
        getRecords: "GET /api/generic/{doctype}",
        create: "POST /api/generic/{doctype}",
        update: "PATCH /api/generic/{doctype}/{id}",
        delete: "DELETE /api/generic/{doctype}/{id}",
        test: "GET /api/generic/test",
        documentation: "See README.md in /api/generic/"
      },
      supportedDocTypes: {
        coreEntities: [
          "patient", "doctor", "consultation", "appointment", 
          "organization", "branch", "department", "user"
        ],
        medicalRecords: [
          "vitals", "prescription", "clinicalNote", "diagnosticReport",
          "immunization", "procedure", "allergyIntolerance"
        ],
        administrative: [
          "invoice", "labTestRequest", "queueStatus", "careType"
        ],
        abdmHealthRecords: [
          "abhaProfile", "consent", "careContext", "documentReference",
          "fhirBundle", "fhirResource"
        ],
        staffScheduling: [
          "staff", "doctorSchedule", "scheduleSlot", "scheduleOverride"
        ]
      },
      totalSupportedTypes: Object.keys(DOCTYPE_MODEL_MAP).length,
      features: [
        "Authentication required",
        "Organization-based data isolation", 
        "Soft delete support",
        "Audit logging",
        "Input validation",
        "Pagination",
        "Search and filtering",
        "Relationship includes",
        "Field selection"
      ],
      usage: {
        authentication: "Session-based authentication required",
        authorization: "Organization-based access control",
        pagination: "Default 50 records per page, max 100",
        search: "Text search on searchable fields",
        filtering: "Use filter_{fieldName}=value",
        sorting: "Use sort_{fieldName}=asc|desc",
        includes: "Use include=relation1,relation2",
        fieldSelection: "Use fields=field1,field2"
      },
      examples: {
        testSpecificDoctype: "/api/generic/test?doctype=patient",
        getPatients: "/api/generic/patient?page=1&limit=20",
        getPatientWithIncludes: "/api/generic/patient/123?include=abhaProfile,primaryBranch",
        searchPatients: "/api/generic/patient?search=john&filter_status=active",
        createPatient: {
          method: "POST",
          url: "/api/generic/patient",
          headers: { "Content-Type": "application/json" },
          body: {
            firstName: "John",
            lastName: "Doe", 
            dateOfBirth: "1990-01-01",
            gender: "male",
            phone: "+**********"
          }
        }
      }
    });

  } catch (error) {
    console.error("Error in generic API test endpoint:", error);
    return NextResponse.json({
      success: false,
      error: "Test endpoint failed",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

/**
 * POST /api/generic/test - Test creating a sample record
 * 
 * This endpoint can be used to test the create functionality
 * with sample data for different doctypes.
 */
export async function POST(req: NextRequest) {
  try {
    const { doctype, testData } = await req.json();

    if (!doctype) {
      return NextResponse.json({
        error: "doctype is required",
        example: {
          doctype: "patient",
          testData: { /* optional test data */ }
        }
      }, { status: 400 });
    }

    // Validate doctype
    const modelName = DOCTYPE_MODEL_MAP[doctype.toLowerCase()];
    if (!modelName) {
      return NextResponse.json({
        error: "Invalid DOCTYPE",
        doctype,
        supportedTypes: Object.keys(DOCTYPE_MODEL_MAP)
      }, { status: 400 });
    }

    // Generate sample test data if not provided
    const sampleData = testData || generateSampleData(doctype);

    return NextResponse.json({
      success: true,
      message: `Test data generated for ${doctype}`,
      doctype,
      modelName,
      testData: sampleData,
      instructions: {
        step1: "Copy the testData object",
        step2: `Make a POST request to /api/generic/${doctype}`,
        step3: "Use the testData as the request body",
        note: "Make sure you have valid authentication cookies"
      },
      curlExample: `curl -X POST "http://localhost:3000/api/generic/${doctype}" \\
  -H "Content-Type: application/json" \\
  -H "Cookie: your-session-cookie" \\
  -d '${JSON.stringify(sampleData, null, 2)}'`
    });

  } catch (error) {
    console.error("Error in generic API test POST:", error);
    return NextResponse.json({
      success: false,
      error: "Test POST failed",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

// Helper function to generate sample data for testing
function generateSampleData(doctype: string): any {
  const sampleDataMap: Record<string, any> = {
    patient: {
      firstName: "Test",
      lastName: "Patient",
      dateOfBirth: "1990-01-01",
      gender: "male",
      phone: "+**********",
      email: "<EMAIL>",
      address: "123 Test Street",
      city: "Test City",
      state: "Test State",
      pincode: "12345"
    },
    consultation: {
      patientId: "patient-id-here",
      doctorId: "doctor-id-here", 
      branchId: "branch-id-here",
      consultationDate: new Date().toISOString(),
      status: "in-progress"
    },
    vitals: {
      consultationId: "consultation-id-here",
      patientId: "patient-id-here",
      doctorId: "doctor-id-here",
      bloodPressureSystolic: 120,
      bloodPressureDiastolic: 80,
      pulse: 72,
      temperature: 98.6,
      height: 170,
      weight: 70
    },
    prescription: {
      consultationId: "consultation-id-here",
      patientId: "patient-id-here",
      doctorId: "doctor-id-here",
      prescriptionDate: new Date().toISOString(),
      instructions: "Take as prescribed"
    },
    clinicalNote: {
      consultationId: "consultation-id-here",
      patientId: "patient-id-here",
      doctorId: "doctor-id-here",
      content: "Test clinical note content",
      chiefComplaints: "Test complaint",
      noteType: "general"
    }
  };

  return sampleDataMap[doctype] || {
    note: `No sample data available for ${doctype}`,
    instruction: "Please provide your own test data"
  };
}
