export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

/**
 * Get ABHA Profile Details
 * POST /api/abdm/abha-profile/get-details
 */
export async function POST(req: NextRequest) {
  try {
    // Get the ABDM API URL
    const ABDM_API_URL = process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL;

    // Log the ABDM API URL for debugging
    console.log("ABDM API URL:", ABDM_API_URL);

    // Get the token from the request
    const { token } = await req.json();

    console.log("Received token for profile details:", token);

    // Validate required fields
    if (!token) {
      console.error("Token is missing in the request");
      return NextResponse.json({ error: "Token is required" }, { status: 400 });
    }

    // Call ABDM API to get profile details
    console.log(
      `Making API call to ${ABDM_API_URL}/v3/profile/account with token`,
    );

    // Check if ABDM API URL is defined
    if (!ABDM_API_URL) {
      console.error("ABDM API URL is not defined");
      return NextResponse.json(
        { error: "ABDM API URL is not defined" },
        { status: 500 },
      );
    }

    // First, get an access token using the session API
    try {
      // Generate a unique request ID and timestamp for the request
      const requestId = crypto.randomUUID();
      const timestamp = new Date().toISOString();

      // Get client credentials from environment variables
      const clientId = process.env.NEXT_PUBLIC_ABDM_CLIENT_ID;
      const clientSecret = process.env.ABDM_CLIENT_SECRET;

      if (!clientId || !clientSecret) {
        console.error("ABDM client credentials are not defined");
        return NextResponse.json(
          { error: "ABDM client credentials are not defined" },
          { status: 500 },
        );
      }

      console.log("Getting access token from session API");

      // Call the session API to get an access token
      const sessionResponse = await fetch(
        `https://dev.abdm.gov.in/api/hiecm/gateway/v3/sessions`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "REQUEST-ID": requestId,
            TIMESTAMP: timestamp,
            "X-CM-ID": "sbx",
          },
          body: JSON.stringify({
            clientId,
            clientSecret,
            grantType: "client_credentials",
          }),
        },
      );

      if (!sessionResponse.ok) {
        console.error(
          "Failed to get access token:",
          await sessionResponse.text(),
        );
        return NextResponse.json(
          { error: "Failed to get access token" },
          { status: sessionResponse.status },
        );
      }

      const sessionData = await sessionResponse.json();
      const accessToken = sessionData.accessToken;

      console.log("Access token obtained successfully");

      // Now call the profile API with both the access token and X-token
      // The correct endpoint is /v3/profile/account based on the ABDM API documentation
      const apiUrl = `${ABDM_API_URL}/v3/profile/account`;
      console.log("Request URL:", apiUrl);

      // Generate new request ID and timestamp for the profile API call
      const profileRequestId = crypto.randomUUID();
      const profileTimestamp = new Date().toISOString();

      console.log("Request headers:", {
        "Content-Type": "application/json",
        "X-token": token ? "Bearer token-exists" : "Bearer token-missing",
        "REQUEST-ID": profileRequestId,
        TIMESTAMP: profileTimestamp,
        Authorization: "Bearer access-token-exists",
      });

      const response = await fetch(apiUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-token": token.startsWith("Bearer ") ? token : `Bearer ${token}`,
          "REQUEST-ID": profileRequestId,
          TIMESTAMP: profileTimestamp,
          Authorization: `Bearer ${accessToken}`,
        },
      });

      console.log("Profile API response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Profile API error response:", errorText);

        // Try to log more details about the error
        try {
          const errorJson = JSON.parse(errorText);
          console.error("Error details:", errorJson);
        } catch (parseError) {
          console.error("Could not parse error response as JSON:", errorText);
        }

        return NextResponse.json(
          { error: `Failed to get profile details: ${errorText}` },
          { status: response.status },
        );
      }

      const data = await response.json();
      console.log("Profile API response data:", JSON.stringify(data, null, 2));

      // Return the profile details
      return NextResponse.json(data);
    } catch (error) {
      console.error("Error calling profile API:", error);
      return NextResponse.json(
        { error: "Failed to call profile API" },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Error getting profile details:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Failed to get profile details",
      },
      { status: 500 },
    );
  }
}
