"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useOrganization } from "./organization-context";
import { toast } from "sonner";

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  bloodGroup?: string;
  maritalStatus?: string;
  occupation?: string;
  email?: string;
  phone: string;
  alternatePhone?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country: string;
  allergies?: string;
  chronicDiseases?: string;
  currentMedications?: string;
  familyMedicalHistory?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelation?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  insuranceExpiryDate?: string;
  registrationDate: string;
  status: string;
  primaryBranchId: string;
  primaryBranch?: {
    id: string;
    name: string;
  };
  organizationId: string;
  userId?: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  // ABHA Information
  abhaProfile?: {
    id: string;
    abhaNumber: string;
    abhaAddress?: string;
    healthIdNumber?: string;
    abhaCardUrl?: string;
    abhaStatus?: string;
    organizationId: string;
    patientId: string;
    createdAt: string;
    updatedAt: string;
  };
  linkTokenRequests?: {
    id: string;
    status: string;
    createdAt: string;
    updatedAt: string;
  }[];
  abhaLinkTokens?: {
    id: string;
    status: string;
    linkToken: string;
    linkTokenExpiry: string;
    createdAt: string;
    updatedAt: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

interface PatientContextType {
  patients: Patient[];
  isLoading: boolean;
  refreshPatients: (status?: string, search?: string) => Promise<void>;
}

const PatientContext = createContext<PatientContextType | undefined>(undefined);

export function PatientProvider({ children }: { children: ReactNode }) {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentOrganization } = useOrganization();

  const refreshPatients = async (status?: string, search?: string) => {
    if (!currentOrganization) {
      setPatients([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    try {
      // Use the new generic API for better performance and consistency
      const params = new URLSearchParams();

      if (status && status !== "all") {
        params.append("filter_status", status);
      }

      if (search) {
        params.append("search", search);
      }

      // Default pagination
      params.append("limit", "50");
      params.append("page", "1");

      // Fetch patients from the new generic API
      const response = await fetch(`/api/generic/patient?${params.toString()}`);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fetch patients");
      }

      const data = await response.json();
      setPatients(data.data || []);
    } catch (error) {
      console.error("Error fetching patients:", error);

      // Fallback to old API if new one fails
      try {
        const params = new URLSearchParams();

        if (status) {
          params.append("status", status);
        }

        if (search) {
          params.append("search", search);
        }

        const fallbackResponse = await fetch(`/api/patients?${params.toString()}`);

        if (fallbackResponse.ok) {
          const fallbackData = await fallbackResponse.json();
          setPatients(fallbackData.patients || []);
          return;
        }
      } catch (fallbackError) {
        console.error("Fallback API also failed:", fallbackError);
      }

      toast.error("Failed to load patients");
      setPatients([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh patients when the organization or branch changes
  useEffect(() => {
    if (currentOrganization) {
      refreshPatients();
    }
  }, [currentOrganization?.id]);

  return (
    <PatientContext.Provider value={{ patients, isLoading, refreshPatients }}>
      {children}
    </PatientContext.Provider>
  );
}

export function usePatient() {
  const context = useContext(PatientContext);
  if (context === undefined) {
    throw new Error("usePatient must be used within a PatientProvider");
  }
  return context;
}
