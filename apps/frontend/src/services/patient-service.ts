/**
 * Patient Service
 *
 * This service handles patient-related operations:
 * - Fetching patient details
 * - Fetching patient allergies
 * - Fetching patient immunizations
 * - Fetching patient documents
 */

import { Fetch } from "./fetch";

// Get patient allergies
export async function getPatientAllergies(patientId: string) {
  try {
    const response = await Fetch.get(`/api/patients/${patientId}/allergies`);
    return response;
  } catch (error) {
    console.error("Error fetching patient allergies:", error);
    throw error;
  }
}

// Get patient immunizations
export async function getPatientImmunizations(patientId: string) {
  try {
    const response = await Fetch.get(
      `/api/patients/${patientId}/immunizations`,
    );
    return response;
  } catch (error) {
    console.error("Error fetching patient immunizations:", error);
    throw error;
  }
}

// Get patient documents
export async function getPatientDocuments(patientId: string) {
  try {
    const response = await Fetch.get(`/api/patients/${patientId}/documents`);
    return response;
  } catch (error) {
    console.error("Error fetching patient documents:", error);
    throw error;
  }
}

// Get patient details
export async function getPatientDetails(patientId: string) {
  try {
    const response = await Fetch.get(`/api/patients/${patientId}`);
    return response;
  } catch (error) {
    console.error("Error fetching patient details:", error);
    throw error;
  }
}

// Get patient by ID (alias for getPatientDetails for backward compatibility)
export async function getPatientById(patientId: string) {
  try {
    const response = await Fetch.get(`/api/patients/${patientId}`);
    return response;
  } catch (error) {
    console.error("Error fetching patient details:", error);
    throw error;
  }
}

// Search patient by phone number
export async function searchPatientByPhone(phone: string) {
  try {
    // Import the server action dynamically to avoid circular dependencies
    const { searchPatientByPhone: searchPatientByPhoneAction } = await import("@/app/(app)/patients/actions");
    const response = await searchPatientByPhoneAction(phone);
    return response;
  } catch (error) {
    console.error("Error searching patient by phone:", error);
    throw error;
  }
}

// Search ABHA by mobile number
export async function searchAbhaByMobile(mobile: string) {
  try {
    const response = await Fetch.post("/api/abdm/abha-search/mobile", {
      mobile,
    });

    console.log("Patient service ABHA search response:", response);

    // Check if the request was successful
    if (response.success) {
      // Return the actual data (which should be the ABHA array)
      return response.data;
    } else {
      // Handle error cases - throw an error with the message
      throw new Error(response.error || "Failed to search ABHA");
    }
  } catch (error) {
    console.error("Error searching ABHA by mobile:", error);
    throw error;
  }
}

// Create patient allergy
export async function createPatientAllergy(
  patientId: string,
  allergyData: any,
) {
  try {
    const response = await Fetch.post(
      `/api/patients/${patientId}/allergies`,
      allergyData,
    );
    return response;
  } catch (error) {
    console.error("Error creating patient allergy:", error);
    throw error;
  }
}

// Create patient immunization
export async function createPatientImmunization(
  patientId: string,
  immunizationData: any,
) {
  try {
    const response = await Fetch.post(
      `/api/patients/${patientId}/immunizations`,
      immunizationData,
    );
    return response;
  } catch (error) {
    console.error("Error creating patient immunization:", error);
    throw error;
  }
}

// Create patient document
export async function createPatientDocument(
  patientId: string,
  documentData: any,
) {
  try {
    const response = await Fetch.post(
      `/api/patients/${patientId}/documents`,
      documentData,
    );
    return response;
  } catch (error) {
    console.error("Error creating patient document:", error);
    throw error;
  }
}

// Update patient allergy
export async function updatePatientAllergy(
  patientId: string,
  allergyId: string,
  allergyData: any,
) {
  try {
    const response = await Fetch.patch(
      `/api/patients/${patientId}/allergies/${allergyId}`,
      allergyData,
    );
    return response;
  } catch (error) {
    console.error("Error updating patient allergy:", error);
    throw error;
  }
}

// Update patient immunization
export async function updatePatientImmunization(
  patientId: string,
  immunizationId: string,
  immunizationData: any,
) {
  try {
    const response = await Fetch.patch(
      `/api/patients/${patientId}/immunizations/${immunizationId}`,
      immunizationData,
    );
    return response;
  } catch (error) {
    console.error("Error updating patient immunization:", error);
    throw error;
  }
}

// Update patient document
export async function updatePatientDocument(
  patientId: string,
  documentId: string,
  documentData: any,
) {
  try {
    const response = await Fetch.patch(
      `/api/patients/${patientId}/documents/${documentId}`,
      documentData,
    );
    return response;
  } catch (error) {
    console.error("Error updating patient document:", error);
    throw error;
  }
}

// Delete patient allergy
export async function deletePatientAllergy(
  patientId: string,
  allergyId: string,
) {
  try {
    const response = await Fetch.delete(
      `/api/patients/${patientId}/allergies/${allergyId}`,
    );
    return response;
  } catch (error) {
    console.error("Error deleting patient allergy:", error);
    throw error;
  }
}

// Delete patient immunization
export async function deletePatientImmunization(
  patientId: string,
  immunizationId: string,
) {
  try {
    const response = await Fetch.delete(
      `/api/patients/${patientId}/immunizations/${immunizationId}`,
    );
    return response;
  } catch (error) {
    console.error("Error deleting patient immunization:", error);
    throw error;
  }
}

// Delete patient document
export async function deletePatientDocument(
  patientId: string,
  documentId: string,
) {
  try {
    const response = await Fetch.delete(
      `/api/patients/${patientId}/documents/${documentId}`,
    );
    return response;
  } catch (error) {
    console.error("Error deleting patient document:", error);
    throw error;
  }
}

// Delete patient
export async function deletePatient(patientId: string) {
  try {
    const response = await Fetch.delete(`/api/patients/${patientId}`);
    return response;
  } catch (error) {
    console.error("Error deleting patient:", error);
    throw error;
  }
}

// Combined search function for phone number with ABHA integration
export async function searchPhoneAndCheckABHA(phone: string) {
  try {
    // First, search for existing patient in DB
    const patientResult = await searchPatientByPhone(phone);

    let abhaResult = null;
    let abhaError = null;

    // Then search for ABHA regardless of patient existence
    try {
      abhaResult = await searchAbhaByMobile(phone);
    } catch (error) {
      console.log("ABHA search failed or no ABHA found:", error);
      abhaError = error instanceof Error ? error.message : "ABHA search failed";
    }

    return {
      patient: patientResult?.patient || null,
      abhaData: abhaResult || null,
      abhaError,
      hasPatient: !!patientResult?.patient,
      hasAbha: !!abhaResult,
      patientHasAbhaProfile: !!patientResult?.patient?.abhaProfile,
    };
  } catch (error) {
    console.error("Error in combined search:", error);
    throw error;
  }
}

// Utility function to detect if input is a phone number
export function isPhoneNumber(input: string): boolean {
  // Remove any non-digit characters
  const cleanInput = input.replace(/\D/g, "");
  // Check if it's exactly 10 digits (Indian mobile number)
  return cleanInput.length === 10 && /^\d{10}$/.test(cleanInput);
}

// Utility function to detect if input is an ABHA address
export function isAbhaAddress(input: string): boolean {
  // ABHA address format: username@domain (e.g., john.doe@abdm, user123@sbx)
  const abhaAddressPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+$/;
  return abhaAddressPattern.test(input.trim());
}


