{"name": "arancare-security-tests", "version": "1.0.0", "description": "Comprehensive security and functional testing for AranCare healthcare application", "scripts": {"test": "mocha --recursive --timeout 30000", "test:security": "mocha security/*.test.js --timeout 30000", "test:abdm": "mocha abdm/*.test.js --timeout 30000", "test:api": "mocha api/*.test.js --timeout 30000", "test:ui": "playwright test", "test:all": "npm run test:security && npm run test:abdm && npm run test:api", "test:watch": "mocha --recursive --watch --timeout 30000", "test:coverage": "nyc mocha --recursive --timeout 30000", "test:report": "mocha --recursive --timeout 30000 --reporter mochaw<PERSON><PERSON>"}, "dependencies": {"@playwright/test": "^1.40.0", "axios": "^1.6.0", "chai": "^4.3.10", "mocha": "^10.2.0", "mochawesome": "^7.1.3", "nyc": "^15.1.0", "playwright": "^1.40.0", "supertest": "^6.3.3"}, "devDependencies": {"@types/chai": "^4.3.11", "@types/mocha": "^10.0.6", "@types/supertest": "^2.0.16", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "joi": "^18.0.0"}, "mocha": {"recursive": true, "timeout": 30000, "reporter": "spec", "require": ["./test-setup.js"]}, "nyc": {"reporter": ["text", "html", "lcov"], "exclude": ["tests/**", "node_modules/**"]}}