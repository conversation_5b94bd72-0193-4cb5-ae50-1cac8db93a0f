/**
 * Test Setup Configuration
 * Global setup for all test suites
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.TEST_BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3005';

// Global test configuration
global.TEST_CONFIG = {
  baseUrl: process.env.TEST_BASE_URL,
  timeout: 30000,
  retries: 3
};

// Console logging for test debugging
console.log('Test environment initialized');
console.log('Base URL:', global.TEST_CONFIG.baseUrl);
